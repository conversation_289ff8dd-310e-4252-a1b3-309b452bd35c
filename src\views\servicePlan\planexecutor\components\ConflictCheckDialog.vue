<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    width="500px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item :label="type === 'elder' ? '老人ID' : '执行人ID'" prop="id">
        <el-input 
          v-model="formData.id" 
          :placeholder="`请输入${type === 'elder' ? '老人' : '执行人'}ID`"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="检测日期" prop="dates">
        <el-date-picker
          v-model="formData.dates"
          type="dates"
          placeholder="选择一个或多个日期"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="时间段">
        <div class="flex items-center gap-2">
          <el-time-picker
            v-model="formData.startTime"
            placeholder="开始时间"
            format="HH:mm"
            value-format="HH:mm"
            :controls="false"
            clearable
            class="!w-120px"
          />
          <span>至</span>
          <el-time-picker
            v-model="formData.endTime"
            placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm"
            :controls="false"
            :min-time="formData.startTime"
            clearable
            class="!w-120px"
          />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button 
        type="primary" 
        @click="handleCheck"
      >
        检测冲突
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { PlanExecutorApi } from '@/api/servicePlan/planexecutor'

defineOptions({ name: 'ConflictCheckDialog' })

interface Props {
  type: 'elder' | 'executor'
}

const props = defineProps<Props>()

const title = computed(() => `检测${props.type === 'elder' ? '老人' : '执行人'}时间冲突`)

const dialogVisible = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  id: undefined as number | undefined,
  dates: [] as string[],
  startTime: '',
  endTime: ''
})

// 表单规则
const rules = {
  id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
  dates: [{ required: true, message: '请选择日期', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 检测冲突
const handleCheck = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const params = {
          [props.type === 'elder' ? 'elderId' : 'executorId']: formData.id,
          dates: formData.dates,
          startTime: formData.startTime,
          endTime: formData.endTime
        }
        console.log(params)
        
        const response = props.type === 'elder' 
          ? await PlanExecutorApi.checkElderConflict(params)
          : await PlanExecutorApi.checkExecutorConflict(params)

        if (response.length > 0) {
          ElMessage.warning(`发现 ${response.length} 个时间冲突`)
          // TODO: 显示冲突详情，可以使用 el-table 展示
          showConflictDetails(response)
        } else {
          ElMessage.success('未发现时间冲突')
        }
      } catch (error) {
        console.error('检测冲突失败:', error)
        ElMessage.error('检测失败')
      }
    }
  })
}

// 显示冲突详情
const showConflictDetails = (conflicts: ConflictInfo[]) => {
  // TODO: 实现冲突详情的展示
  console.log('冲突详情:', conflicts)
}

// 重置表单
const resetForm = () => {
  formData.id = undefined
  formData.dates = []
  formData.startTime = ''
  formData.endTime = ''
  formRef.value?.resetFields()
}

// 暴露方法
defineExpose({
  open: () => {
    dialogVisible.value = true
  }
})
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 8px;
}

:deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-time-picker) {
  width: 120px !important;
}
</style> 