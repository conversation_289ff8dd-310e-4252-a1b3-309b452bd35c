<script setup lang="ts">
import { ResultApi, ResultVO } from '@/api/evaluation/result'
import { TemplateApi, TemplateVO } from '@/api/evaluation/template'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'

defineOptions({
    name: 'ElderEval'
})

const router = useRouter()
const message = useMessage()
const userStore = useUserStore()

// 格式化当前日期时间
const formatCurrentDateTime = () => {
    return new Date().getTime()
}

// 弹窗相关
const dialogVisible = ref(false)
const formLoading = ref(false)
const formData = reactive({
    elderId: undefined,
    elderName: '',
    templateId: undefined,
    templateName: '',
    evaluatorId: userStore.user.id,
    evaluatorName: userStore.user.nickname,
    evaluationTime: formatCurrentDateTime(),
    // result: '123'
})

// 模板列表
const templateList = ref<TemplateVO[]>([])
// 老人列表
const elderList = ref<ArchivesProfileVO[]>([])

// 获取模板列表
const getTemplateList = async () => {
    try {
        const res = await TemplateApi.getTemplatePage({
            pageNo: 1,
            pageSize: 100,
            status: 0
        })
        templateList.value = res.list
    } catch (error) {
        console.error('获取模板列表失败:', error)
        message.error('获取模板列表失败')
    }
}

// 获取老人列表
const getElderList = async () => {
    try {
        const res = await ArchivesProfileApi.getArchivesProfilePage({
            pageNo: 1,
            pageSize: 100
        })
        elderList.value = res.list
    } catch (error) {
        console.error('获取老人列表失败:', error)
        message.error('获取老人列表失败')
    }
}

// 模板选择变化时
const handleTemplateChange = (templateId: number) => {
    const template = templateList.value.find(t => t.id === templateId)
    if (template) {
        formData.templateName = template.name
    }
}

// 老人选择变化时
const handleElderChange = (elderId: number) => {
    const elder = elderList.value.find(e => e.id === elderId)
    if (elder) {
        formData.elderName = elder.name
    }
}

// 打开弹窗
const openDialog = () => {
    // 重置表单数据
    formData.elderId = undefined
    formData.elderName = ''
    formData.templateId = undefined
    formData.templateName = ''
    formData.evaluationTime = formatCurrentDateTime()
    // 评估师信息保持不变，使用当前登录用户
    formData.evaluatorId = userStore.user.id
    formData.evaluatorName = userStore.user.nickname

    dialogVisible.value = true
    // 获取所有需要的数据
    getTemplateList()
    getElderList()
}

// 确认创建评估
const handleConfirm = async () => {
    if (!formData.templateId) {
        message.error('请选择评估模板')
        return
    }
    if (!formData.elderId) {
        message.error('请选择老人')
        return
    }

    // 跳转到评估页面，带上必要的参数
    router.push({
        path: '/evaluation/elder-eval-add',
        query: {
            elderId: formData.elderId,
            elderName: formData.elderName,
            templateId: formData.templateId,
            templateName: formData.templateName,
            evaluatorId: formData.evaluatorId,
            evaluatorName: formData.evaluatorName,
            evaluationTime: formData.evaluationTime
        }
    })

    dialogVisible.value = false
}
</script>

<template>
    <ContentWrap>
        <!-- 操作按钮 -->
        <el-button type="primary" @click="openDialog">
            <Icon icon="ep:plus" class="mr-5px" />新增评估
        </el-button>

        <!-- 新增评估弹窗 -->
        <el-dialog v-model="dialogVisible" title="新增评估" width="500px" :close-on-click-modal="false">
            <el-form :model="formData" label-width="100px" v-loading="formLoading">
                <el-form-item label="选择老人" required>
                    <el-select v-model="formData.elderId" placeholder="请选择老人" @change="handleElderChange" filterable>
                        <el-option v-for="item in elderList" :key="item.id" :label="`${item.name}(${item.idNumber})`"
                            :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="评估模板" required>
                    <el-select v-model="formData.templateId" placeholder="请选择评估模板" @change="handleTemplateChange">
                        <el-option v-for="item in templateList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="评估师">
                    <el-input v-model="formData.evaluatorName" disabled />
                </el-form-item>
                <!-- <el-form-item label="评估时间" required>
                    <el-date-picker v-model="formData.evaluationTime" type="datetime" placeholder="请选择评估时间"
                        value-format="x" :default-time="new Date(2000, 1, 1, 0, 0, 0)" />
                </el-form-item> -->
            </el-form>
            <template #footer>
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleConfirm" :loading="formLoading">确定</el-button>
            </template>
        </el-dialog>
    </ContentWrap>
</template>

<style lang="scss" scoped></style>
