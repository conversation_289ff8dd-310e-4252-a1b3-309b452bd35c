import request from '@/config/axios'

// 评估模板规则 VO
export interface TemplateRuleVO {
  id: number // 规则ID
  name: string // 规则名称
  templateId: number // 模板ID
  templateName: string // 模板名称
  itemId: number // 项ID
  itemName: string // 项名称
  ruleSchema: string // 规则JSON
}

// 评估模板规则 API
export const TemplateRuleApi = {
  // 查询评估模板规则分页
  getTemplateRulePage: async (params: any) => {
    return await request.get({ url: `/evaluation/template-rule/page`, params })
  },

  // 查询评估模板规则详情
  getTemplateRule: async (id: number) => {
    return await request.get({ url: `/evaluation/template-rule/get?id=` + id })
  },

  // 新增评估模板规则
  createTemplateRule: async (data: TemplateRuleVO) => {
    return await request.post({ url: `/evaluation/template-rule/create`, data })
  },

  // 修改评估模板规则
  updateTemplateRule: async (data: TemplateRuleVO) => {
    return await request.put({ url: `/evaluation/template-rule/update`, data })
  },

  // 删除评估模板规则
  deleteTemplateRule: async (id: number) => {
    return await request.delete({ url: `/evaluation/template-rule/delete?id=` + id })
  },

  // 导出评估模板规则 Excel
  exportTemplateRule: async (params) => {
    return await request.download({ url: `/evaluation/template-rule/export-excel`, params })
  },
}
