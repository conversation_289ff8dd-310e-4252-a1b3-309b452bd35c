<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="项名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入项名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="模板ID" prop="templateId">
        <el-input v-model="queryParams.templateId" placeholder="请输入模板ID" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <!-- <el-form-item label="项JSON" prop="itemSchema">
        <el-input
          v-model="queryParams.itemSchema"
          placeholder="请输入项JSON"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['evaluation:template-item:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <el-button type="success" plain @click="handleExport" :loading="exportLoading"
          v-hasPermi="['evaluation:template-item:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="项ID" align="center" prop="id" />
      <el-table-column label="项名称" align="center" prop="name" />
      <el-table-column label="模板ID" align="center" prop="templateId" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="项预览" align="center" width="120">
        <template #default="scope">
          <el-button link type="primary" @click="handlePreview(scope.row)">
            <Icon icon="ep:view" class="mr-5px" />查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center" min-width="120px">
        <!-- <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['evaluation:template-item:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['evaluation:template-item:delete']"
          >
            删除
          </el-button>
        </template> -->
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TemplateItemForm ref="formRef" @success="getList" />

  <!-- 模板项预览对话框 -->
  <el-dialog v-model="previewVisible" title="模板项预览" width="60%" destroy-on-close :close-on-click-modal="false"
    @closed="handlePreviewClose">
    <div class="preview-form" v-if="currentItem">
      <form-create v-if="previewVisible" v-model="previewForm" v-model:api="previewApi" :rule="itemRule"
        :option="itemOption" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TemplateItemApi, TemplateItemVO } from '@/api/evaluation/templateitem'
import TemplateItemForm from './TemplateItemForm.vue'
import formCreate from '@form-create/element-ui'

/** 评估模板项 列表 */
defineOptions({ name: 'TemplateItem' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TemplateItemVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  templateId: undefined,
  templateName: undefined,
  itemSchema: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 预览相关的变量
const previewVisible = ref(false)
const previewForm = ref({})
const previewApi = ref(null)
const currentItem = ref<TemplateItemVO | null>(null)
const itemRule = ref([])
const itemOption = ref({
  submitBtn: { show: false },
  resetBtn: { show: false }
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TemplateItemApi.getTemplateItemPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TemplateItemApi.deleteTemplateItem(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TemplateItemApi.exportTemplateItem(queryParams)
    download.excel(data, '评估模板项.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 处理预览 */
const handlePreview = (row: TemplateItemVO) => {
  currentItem.value = row
  previewVisible.value = true

  // 解析模板项数据
  try {
    const schema = JSON.parse(row.itemSchema)
    itemRule.value = [schema.rule] || [] // 注意这里是单个规则，需要包装成数组
  } catch (error) {
    console.error('解析模板项数据失败:', error)
    ElMessage.error('模板项数据格式错误')
  }
}

/** 处理预览对话框关闭 */
const handlePreviewClose = () => {
  // 清空预览相关的数据
  currentItem.value = null
  previewForm.value = {}
  previewApi.value = null
  itemRule.value = []
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.preview-form {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;

  :deep(.el-form) {
    max-width: 800px;
    margin: 0 auto;
  }
}
</style>