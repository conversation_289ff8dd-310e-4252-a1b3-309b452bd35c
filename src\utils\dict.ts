/**
 * 数据字典工具类
 */
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  colorType: ElementPlusInfoType | ''
  cssClass: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export interface StringDictDataType extends DictDataType {
  value: string
}

export const getDictOptions = (dictType: string) => {
  return dictStore.getDictByType(dictType) || []
}

export const getIntDictOptions = (dictType: string): NumberDictDataType[] => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '')
    })
  })
  return dictOption
}

export const getStrDictOptions = (dictType: string) => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: StringDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  TERMINAL = 'terminal', // 终端
  DATE_INTERVAL = 'date_interval', // 数据间隔

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX = 'system_user_sex',

  // ========== AI - 人工智能模块  ==========
  AI_PLATFORM = 'ai_platform', // AI 平台
  AI_MODEL_TYPE = 'ai_model_type', // AI 模型类型
  AI_IMAGE_STATUS = 'ai_image_status', // AI 图片状态
  AI_MUSIC_STATUS = 'ai_music_status', // AI 音乐状态
  AI_GENERATE_MODE = 'ai_generate_mode', // AI 生成模式
  AI_WRITE_TYPE = 'ai_write_type', // AI 写作类型
  AI_WRITE_LENGTH = 'ai_write_length', // AI 写作长度
  AI_WRITE_FORMAT = 'ai_write_format', // AI 写作格式
  AI_WRITE_TONE = 'ai_write_tone', // AI 写作语气
  AI_WRITE_LANGUAGE = 'ai_write_language', // AI 写作语言

  // ========== SERVICE_PLAN模块 ==========
  SERVICE_PLAN_STATUS = 'service_plan_status',
  SERVICE_PLAN_TASK_STATUS = 'service_plan_task_status',
  SERVICE_PLAN_TASK_IS_RECURRING = 'service_plan_task_is_recurring',
  SERVICE_PLAN_TASK_PRIORITY = 'service_plan_task_priority',
  SERVICE_PLAN_CONFLICT_LOG_RESOLVE_STATUS = 'service_plan_conflict_log_resolve_status',
  PLAN_CATEGORY_REL_ACTIVE = 'plan_category_rel_active',
  PLAN_CATEGORY_REL_PRIORITY = 'plan_category_rel_priority',

  // ========== EVALUATION 模块 ==========
  EVALUATION_TEMPLATE_STATUS = 'evaluation_template_status',
  EVALUATION_TEMPLATE_TYPE = 'evaluation_template_type',
  EVALUATION_LIST_STATUS = 'evaluation_list_status',
  EVALUATION_LIST_EXECUTION_STATUS = 'evaluation_list_execution_status',
  EVALUATION_RESULT_TYPE = 'evaluation_result_type',


  // ========== ELDER_ARCHIVES 模块 ==========
  ELDER_SERVICE_LEVEL = 'elder_service_level', // 老人服务等级
  EDUCATION_LEVEL = 'education_level', // 文化程度
  MARITAL_STATUS = 'marital_status', // 婚姻状况
  EMERGENCY_RELATION = 'emergency_relation', // 与紧急联系人关系
  HOUSEHOLD_TYPE = 'household_type', // 户籍类型
  RESIDENCE_TYPE = 'residence_type', // 居住类型
  MEDICAL_INSURANCE = 'medical_insurance', // 医疗保险
  OCCUPATION_TYPE = 'occupation_type', // 职业类型
  INCOME_SOURCE = 'income_source', // 经济来源
  ELDER_SELF_CARE_ABILITY = 'elder_self_care_ability', // 老人自理能力
  ELDER_MENTAL_STATE = 'elder_mental_state', // 老人精神状态
  PHYSICAL_DISEASE = 'physical_disease', // 躯体疾病
  MENTAL_DISEASE = 'mental_disease', // 精神疾病
  FALL_HISTORY = 'fall_history', // 跌倒史
  WANDERING_HISTORY = 'wandering_history', // 走失史
  HOSPITALIZATION_HISTORY = 'hospitalization_history', // 住院史
  DIAGNOSIS_TYPE = 'diagnosis_type', // 诊断类型
  USAGE_METHOD = 'usage_method', // 服用方法
  MEDICATION_FREQUENCY = 'medication_frequency', // 用药频率
  ICD10_COMMON_DISEASE = 'icd10_common_disease', // ICD-10常见疾病编码
}
