<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <!-- 左侧区域 - 占3/5宽度 -->
        <el-col :span="14">
          <el-form-item label="老人ID" prop="elderId">
            <el-input v-model="formData.elderId" placeholder="请输入老人ID" />
          </el-form-item>
          <el-form-item label="老人姓名" prop="elderName">
            <el-input v-model="formData.elderName" placeholder="请输入老人姓名" />
          </el-form-item>
          <el-form-item label="模板ID" prop="templateId">
            <el-input v-model="formData.templateId" placeholder="请输入模板ID" />
          </el-form-item>
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="formData.templateName" placeholder="请输入模板名称" />
          </el-form-item>
          <el-form-item label="评估结果" prop="result">
            <el-input v-model="formData.result" placeholder="请输入评估结果" type="textarea" :rows="4" />
          </el-form-item>
        </el-col>

        <!-- 右侧区域 - 占2/5宽度 -->
        <el-col :span="10">
          <el-form-item label="评估师ID" prop="evaluatorId">
            <el-input v-model="formData.evaluatorId" placeholder="请输入评估师ID" />
          </el-form-item>
          <el-form-item label="评估师姓名" prop="evaluatorName">
            <el-input v-model="formData.evaluatorName" placeholder="请输入评估师姓名" />
          </el-form-item>
          <el-form-item label="评估时间" prop="evaluationTime">
            <el-date-picker
              v-model="formData.evaluationTime"
              type="date"
              value-format="x"
              placeholder="选择评估时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ResultApi, ResultVO } from '@/api/evaluation/result'

/** 评估结果 表单 */
defineOptions({ name: 'ResultForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  elderName: undefined,
  templateId: undefined,
  templateName: undefined,
  evaluatorId: undefined,
  evaluatorName: undefined,
  evaluationTime: undefined,
  result: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '老人ID不能为空', trigger: 'blur' }],
  elderName: [{ required: true, message: '老人姓名不能为空', trigger: 'blur' }],
  templateId: [{ required: true, message: '模板ID不能为空', trigger: 'blur' }],
  templateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
  evaluatorId: [{ required: true, message: '评估师ID不能为空', trigger: 'blur' }],
  evaluatorName: [{ required: true, message: '评估师姓名不能为空', trigger: 'blur' }],
  evaluationTime: [{ required: true, message: '评估时间不能为空', trigger: 'blur' }],
  result: [{ required: true, message: '评估结果不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ResultApi.getResult(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ResultVO
    if (formType.value === 'create') {
      await ResultApi.createResult(data)
      message.success(t('common.createSuccess'))
    } else {
      await ResultApi.updateResult(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    elderName: undefined,
    templateId: undefined,
    templateName: undefined,
    evaluatorId: undefined,
    evaluatorName: undefined,
    evaluationTime: undefined,
    result: undefined,
  }
  formRef.value?.resetFields()
}
</script>