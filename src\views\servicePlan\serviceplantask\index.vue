<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="护理计划" prop="planId">
        <el-select
          v-model="queryParams.planId"
          placeholder="请选择护理计划"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in planList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择关联老人"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in elderList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务分类" prop="categoryId">
        <el-tree-select
          v-model="queryParams.categoryId"
          :data="categoryTree"
          :props="categoryProps"
          placeholder="请选择任务分类"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="queryParams.executorId"
          placeholder="请选择执行人"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否周期任务" prop="isRecurring">
        <el-select
          v-model="queryParams.isRecurring"
          placeholder="请选择是否周期任务"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select
          v-model="queryParams.priority"
          placeholder="请选择优先级"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['servicePlan:task:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['servicePlan:task:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>

      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="护理计划" align="center">
        <template #default="{ row }">
          {{ formatPlan(row.planId) }}
        </template>
      </el-table-column>
      <el-table-column label="关联老人" align="center">
        <template #default="{ row }">
          {{ formatElder(row.elderId) }}
        </template>
      </el-table-column>
      <el-table-column label="任务名称" align="center" prop="name" />
      <el-table-column label="任务类型" align="center" prop="taskType" />
      
      <el-table-column label="状态" align="center" prop="status" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="是否周期性" align="center" prop="isRecurring">
        <template #default="scope">
          <dict-tag 
            :type="DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING" 
            :value="Number(scope.row.isRecurring)" 
          />
        </template>
      </el-table-column>
      <el-table-column label="推荐执行人" align="center" prop="recommendedExecutorNames" />
      <el-table-column label="开始时间" align="center" prop="startTime" :formatter="(row) => formatTime(row.startTime)" />
      <el-table-column label="结束时间" align="center" prop="endTime" :formatter="(row) => formatTime(row.endTime)" />
      <el-table-column label="优先级" align="center" prop="priority">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY" :value="scope.row.priority" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:task:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['servicePlan:task:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TaskForm 
    ref="formRef" 
    @success="getList" 
    :user-list="userList"
    :elder-list="elderList"
    :plan-list="planList"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ServicePlanApi, ServicePlanSimpleVO } from '@/api/servicePlan/serviceplan'
import TaskForm from './TaskForm.vue'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'

/** 服务任务 列表 */
defineOptions({ name: 'ServicePlanTask' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TaskVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  planId: undefined,
  elderId: undefined,
  categoryId: undefined,
  status: undefined,
  isRecurring: undefined,
  priority: undefined,
  executorId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const exportExecutionSheetLoading = ref(false) // 导出执行表的加载中

// 定义执行人和老人列表的类型
interface SimpleUser {
  id: number
  name: string
}

interface SimpleElder {
  id: number
  name: string
}

// 存储执行人和老人的映射关系
const executorMap = ref<Map<number, string>>(new Map())
const elderMap = ref<Map<number, string>>(new Map())
// 存储服务计划的映射关系
const planMap = ref<Map<number, string>>(new Map())

// 定义列表数据
interface SelectOption {
  id: number
  label: string
  value: number
}

// 下拉框选项列表
const userList = ref<SelectOption[]>([])
const elderList = ref<SelectOption[]>([])
const planList = ref<SelectOption[]>([])

// 添加分类树相关的数据
const categoryTree = ref<any[]>([])
const categoryProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true,
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskApi.deleteTask(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskApi.exportTask(queryParams)
    download.excel(data, '服务任务.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 导出执行表按钮操作 */
const handleExportExecutionSheet = async () => {
  try {
    // 发起导出执行表
    exportExecutionSheetLoading.value = true
    const data = await TaskApi.exportTaskExecutionSheet(queryParams)
    download.excel(data, '服务任务执行表.xls')
  } catch {
  } finally {
    exportExecutionSheetLoading.value = false
  }
}

/** 格式化时间 */
const formatTime = (time: string) => {
  if (!time) return ''
  return time
}

/** 获取执行人列表 */
const getExecutorList = async () => {
  try {
    const data = await getSimpleUserList()
    userList.value = data.map(item => ({
      id: item.id,
      label: item.nickname,
      value: item.id
    }))
    data.forEach(item => {
      executorMap.value.set(Number(item.id), item.nickname)
    })
  } catch (error) {
    console.error('获取执行人列表失败:', error)
  }
}

/** 获取老人列表 */
const getElderList = async () => {
  try {
    const data = await ArchivesProfileApi.getArchivesProfileSimpleList()
    elderList.value = data.map(item => ({
      id: item.id,
      label: item.name,
      value: item.id
    }))
    data.forEach(item => {
      elderMap.value.set(item.id, item.name)
    })
  } catch (error) {
    console.error('获取老人列表失败:', error)
  }
}

/** 获取服务计划列表 */
const getPlanList = async () => {
  try {
    const data = await ServicePlanApi.getServicePlanSimpleList()
    planList.value = data.map(item => ({
      id: item.id,
      label: item.planName,
      value: item.id
    }))
    data.forEach(item => {
      planMap.value.set(item.id, item.planName)
    })
  } catch (error) {
    console.error('获取服务计划列表失败:', error)
  }
}

/** 获取分类树数据 */
const getCategoryTree = async () => {
  try {
    const treeData = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = treeData || []
  } catch (error) {
    console.error('获取分类树失败:', error)
    message.error('获取分类树失败')
  }
}

/** 格式化执行人显示 */
const formatExecutors = (executors: string) => {
  if (!executors) return ''
  try {
    const executorIds = JSON.parse(executors) as number[]
    return executorIds
      .map(id => executorMap.value.get(id) || id)
      .filter(Boolean)
      .join(', ')
  } catch (e) {
    return executors
  }
}

/** 格式化老人显示 */
const formatElder = (elderId: number) => {
  return elderMap.value.get(elderId) || elderId
}

/** 格式化服务计划显示 */
const formatPlan = (planId: number) => {
  return planMap.value.get(planId) || planId
}

/** 初始化 **/
onMounted(async () => {
  await Promise.all([
    getExecutorList(),
    getElderList(),
    getPlanList(),
    getCategoryTree(),
  ])
  getList()
})
</script>

<style scoped>
/* 添加树形选择器样式 */
:deep(.el-tree-select) {
  width: 240px !important;
}
</style>
