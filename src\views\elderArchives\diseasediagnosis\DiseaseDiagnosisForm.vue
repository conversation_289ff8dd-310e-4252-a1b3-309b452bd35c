<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="老人ID" prop="elderId">
        <el-input v-model="formData.elderId" placeholder="请输入老人ID" />
      </el-form-item>
      <el-form-item label="诊断日期" prop="diagnosisDate">
        <el-date-picker
          v-model="formData.diagnosisDate"
          type="date"
          value-format="x"
          placeholder="选择诊断日期"
        />
      </el-form-item>
      <el-form-item label="诊断医生" prop="doctorName">
        <el-input v-model="formData.doctorName" placeholder="请输入诊断医生" />
      </el-form-item>
      <el-form-item label="诊断机构" prop="hospitalName">
        <el-input v-model="formData.hospitalName" placeholder="请输入诊断机构" />
      </el-form-item>
      <el-form-item label="诊断类型" prop="diagnosisType">
        <el-select v-model="formData.diagnosisType" placeholder="请选择诊断类型">
          <el-option v-for="dict in getDictOptions(DICT_TYPE.DIAGNOSIS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DiseaseDiagnosisApi, DiseaseDiagnosisVO } from '@/api/elderArchives/diseasediagnosis'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

/** 老人疾病诊断 表单 */
defineOptions({ name: 'DiseaseDiagnosisForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  diagnosisDate: undefined,
  doctorName: undefined,
  hospitalName: undefined,
  diagnosisType: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '老人ID不能为空', trigger: 'blur' }],
  diagnosisDate: [{ required: true, message: '诊断日期不能为空', trigger: 'blur' }],
  diagnosisType: [{ required: true, message: '诊断类型不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DiseaseDiagnosisApi.getDiseaseDiagnosis(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DiseaseDiagnosisVO
    if (formType.value === 'create') {
      await DiseaseDiagnosisApi.createDiseaseDiagnosis(data)
      message.success(t('common.createSuccess'))
    } else {
      await DiseaseDiagnosisApi.updateDiseaseDiagnosis(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    diagnosisDate: undefined,
    doctorName: undefined,
    hospitalName: undefined,
    diagnosisType: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>