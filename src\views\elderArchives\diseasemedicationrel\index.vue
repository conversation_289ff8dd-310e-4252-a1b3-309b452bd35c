<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="老人姓名" prop="elderName">
            <el-input
              v-model="queryParams.elderName"
              placeholder="请输入老人姓名"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="疾病名称" prop="diseaseName">
            <el-input
              v-model="queryParams.diseaseName"
              placeholder="请输入疾病名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="药物名称" prop="medicineName">
            <el-input
              v-model="queryParams.medicineName"
              placeholder="请输入药物名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['elderArchives:disease-medication-rel:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['elderArchives:disease-medication-rel:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="老人姓名" align="center" prop="elderName" min-width="100" />
      <el-table-column label="疾病名称" align="center" prop="diseaseName" min-width="120" />
      <el-table-column label="诊断日期" align="center" prop="diagnosisDate" min-width="100" />
      <el-table-column label="诊断医生" align="center" prop="doctorName" min-width="100" />
      <el-table-column label="诊断类型" align="center" prop="diagnosisType" min-width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="scope.row.diagnosisType" />
        </template>
      </el-table-column>
      <el-table-column label="药物名称" align="center" prop="medicineName" min-width="120" />
      <el-table-column label="是否长期用药" align="center" prop="isLongTerm" min-width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isLongTerm ? 'success' : 'warning'">
            {{ scope.row.isLongTerm ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['elderArchives:disease-medication-rel:query']"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['elderArchives:disease-medication-rel:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:disease-medication-rel:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DiseaseMedicationRelForm ref="formRef" @success="getList" />

  <!-- 详情弹窗 -->
  <DiseaseMedicationRelDetail ref="detailRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DiseaseMedicationRelApi, DiseaseMedicationRelVO } from '@/api/elderArchives/diseasemedicationrel'
import DiseaseMedicationRelForm from './DiseaseMedicationRelForm.vue'
import DiseaseMedicationRelDetail from './DiseaseMedicationRelDetail.vue'
import { DICT_TYPE } from '@/utils/dict'

/** 疾病与用药关联 列表 */
defineOptions({ name: 'DiseaseMedicationRel' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DiseaseMedicationRelVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  diagnosisId: undefined,
  medicationId: undefined,
  elderName: undefined,
  diseaseName: undefined,
  medicineName: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DiseaseMedicationRelApi.getDiseaseMedicationRelPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 详情按钮操作 */
const detailRef = ref()
const handleDetail = (row: DiseaseMedicationRelVO) => {
  detailRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DiseaseMedicationRelApi.deleteDiseaseMedicationRel(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DiseaseMedicationRelApi.exportDiseaseMedicationRel(queryParams)
    download.excel(data, '疾病与用药关联.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
