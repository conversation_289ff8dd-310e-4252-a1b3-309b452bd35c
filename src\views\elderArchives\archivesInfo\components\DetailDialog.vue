<template>
  <el-dialog
    v-model="dialogVisible"
    title="老人档案详情"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-tabs v-model="activeTab">
      <!-- 基本信息 Tab -->
      <el-tab-pane label="基本信息" name="basic">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:user" class="mr-5px" />
            基本信息
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ detailData.name }}</el-descriptions-item>
          <el-descriptions-item label="性别">
            <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="detailData.gender" />
          </el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ detailData.idNumber }}</el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ formatDate(detailData.birthDate) }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detailData.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="服务等级">
            <dict-tag :type="DICT_TYPE.ELDER_SERVICE_LEVEL" :value="detailData.serviceLevel" />
          </el-descriptions-item>
          <el-descriptions-item label="文化程度">
            <dict-tag :type="DICT_TYPE.EDUCATION_LEVEL" :value="detailData.educationLevel" />
          </el-descriptions-item>
          <el-descriptions-item label="婚姻状况">
            <dict-tag :type="DICT_TYPE.MARITAL_STATUS" :value="detailData.maritalStatus" />
          </el-descriptions-item>
          <el-descriptions-item label="户籍类型">
            <dict-tag :type="DICT_TYPE.HOUSEHOLD_TYPE" :value="detailData.householdType" />
          </el-descriptions-item>
          <el-descriptions-item label="居住类型">
            <template v-if="detailData.residenceType && typeof detailData.residenceType === 'string'">
              <dict-tag :type="DICT_TYPE.RESIDENCE_TYPE" :value="parseInt(detailData.residenceType)" />
            </template>
            <template v-else-if="detailData.residenceType && Array.isArray(detailData.residenceType)">
              <dict-tag v-for="item in detailData.residenceType" 
                       :key="item" 
                       :type="DICT_TYPE.RESIDENCE_TYPE" 
                       :value="item" 
                       class="mr-1" />
            </template>
            <template v-else-if="detailData.residenceType && typeof detailData.residenceType === 'number'">
              <dict-tag :type="DICT_TYPE.RESIDENCE_TYPE" :value="detailData.residenceType" />
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="医疗保险" :span="2">
            <template v-if="detailData.medicalInsurance">
              <dict-tag v-for="item in detailData.medicalInsurance.split(',')" 
                       :key="item" 
                       :type="DICT_TYPE.MEDICAL_INSURANCE" 
                       :value="parseInt(item)" />
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>

      <!-- 扩展信息 Tab -->
      <el-tab-pane label="扩展信息" name="extension">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:document" class="mr-5px" />
            扩展信息
          </div>
        </template>
        <div v-if="extensionData.id" class="extension-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="自理能力">
              <dict-tag :type="DICT_TYPE.ELDER_SELF_CARE_ABILITY" :value="extensionData.selfCareAbility" />
            </el-descriptions-item>
            <el-descriptions-item label="精神状态">
              <dict-tag :type="DICT_TYPE.ELDER_MENTAL_STATE" :value="extensionData.mentalState" />
            </el-descriptions-item>
            <el-descriptions-item label="躯体疾病">
              <template v-if="extensionData.physicalDisease">
                <dict-tag v-for="item in extensionData.physicalDisease.split(',')" 
                          :key="item" 
                          :type="DICT_TYPE.PHYSICAL_DISEASE" 
                          :value="Number(item)" />
              </template>
            </el-descriptions-item>
            <el-descriptions-item label="精神疾病">
              <template v-if="extensionData.mentalDisease">
                <dict-tag v-for="item in extensionData.mentalDisease.split(',')" 
                          :key="item" 
                          :type="DICT_TYPE.MENTAL_DISEASE" 
                          :value="Number(item)" />
              </template>
            </el-descriptions-item>
            <el-descriptions-item label="服药情况">{{ extensionData.medicationStatus }}</el-descriptions-item>
            <el-descriptions-item label="跌倒史">
              <dict-tag :type="DICT_TYPE.FALL_HISTORY" :value="extensionData.fallHistory" />
            </el-descriptions-item>
            <el-descriptions-item label="走失史">
              <dict-tag :type="DICT_TYPE.WANDERING_HISTORY" :value="extensionData.wanderingHistory" />
            </el-descriptions-item>
            <el-descriptions-item label="住院史">
              <dict-tag :type="DICT_TYPE.HOSPITALIZATION_HISTORY" :value="extensionData.hospitalizationHistory" />
            </el-descriptions-item>
            <el-descriptions-item label="其他意外事件">{{ extensionData.otherAccidents }}</el-descriptions-item>
            <el-descriptions-item label="记录日期">{{ formatDate(extensionData.recordDate) }}</el-descriptions-item>
            <el-descriptions-item label="记录人">{{ extensionData.recorder }}</el-descriptions-item>
            <el-descriptions-item label="备注">{{ extensionData.remark }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <el-empty v-else description="暂无扩展信息" />
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleViewDetail">查看完整档案</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ArchivesExtensionApi } from '@/api/elderArchives/archivesExtension'
import type { ArchivesExtensionVO } from '@/api/elderArchives/archivesExtension'

const router = useRouter()
const message = useMessage()

const dialogVisible = ref(false)
const activeTab = ref('basic')
const detailLoading = ref(false)
const detailData = ref<any>({})
const extensionData = ref<ArchivesExtensionVO>({} as ArchivesExtensionVO)

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  activeTab.value = 'basic'
  await loadDetailData(id)
}

/** 加载详情数据 */
const loadDetailData = async (id: number) => {
  detailLoading.value = true
  try {
    // 加载基本信息
    const data = await ArchivesProfileApi.getArchivesProfile(id)
    detailData.value = data
    
    // 加载扩展信息
    await loadExtensionData(id)
  } catch (error: any) {
    message.error('获取详情失败：' + (error.message || '未知错误'))
  } finally {
    detailLoading.value = false
  }
}

/** 加载扩展信息 */
const loadExtensionData = async (elderId: number) => {
  try {
    // 获取最新的一条记录
    const data = await ArchivesExtensionApi.getArchivesExtensionPage({
      elderId,
      pageNo: 1,
      pageSize: 1
    })
    
    if (data && data.list && data.list.length > 0) {
      const info = data.list[0]
      // 处理数字类型字段
      info.selfCareAbility = Number(info.selfCareAbility) || null
      info.mentalState = Number(info.mentalState) || null
      info.fallHistory = Number(info.fallHistory) || null
      info.wanderingHistory = Number(info.wanderingHistory) || null
      info.hospitalizationHistory = Number(info.hospitalizationHistory) || null
      
      extensionData.value = info
    } else {
      extensionData.value = {} as ArchivesExtensionVO
    }
  } catch (error: any) {
    console.error('获取扩展信息失败:', error)
  }
}

/** 查看完整档案 */
const handleViewDetail = () => {
  dialogVisible.value = false
  router.push({
    path: '/elders/archivesInfo/detail',
    query: {
      elder_id: detailData.value.id
    }
  })
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  color: #606266;
}

:deep(.el-descriptions__content) {
  padding: 12px 16px;
}

:deep(.dict-tag) {
  margin-right: 8px;
  margin-bottom: 4px;
}

.extension-content {
  padding: 0;
}
</style>
