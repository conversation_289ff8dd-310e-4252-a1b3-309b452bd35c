<!--
 * @Date: 2025-02-13 16:21:12
 * @Author: xkk
 * @LastEditTime: 2025-02-22 21:43:10
 * @FilePath: \huiyi_manage\src\views\evaluation\elderEval\addEval.vue
-->
<script setup lang="ts">
import { TemplateApi } from '@/api/evaluation/template'
import { ResultApi } from '@/api/evaluation/result'
import formCreate from '@form-create/element-ui'

defineOptions({
    name: 'AddEval'
})

const route = useRoute()
const message = useMessage()
const router = useRouter()

// 表单相关
const previewForm = ref({})
const previewApi = ref(null)
const templateRule = ref([])
const templateOption = ref({
    submitBtn: { show: true },
    resetBtn: { show: true }
})

// 评估基础信息
const evalInfo = reactive({
    elderId: undefined as number | undefined,
    elderName: '',
    templateId: undefined as number | undefined,
    templateName: '',
    evaluatorId: undefined as number | undefined,
    evaluatorName: '',
    evaluationTime: ''
})

// 加载模板数据
const loadTemplate = async () => {
    try {
        const templateId = route.query.templateId as string
        if (!templateId) {
            message.error('模板ID不能为空')
            return
        }

        const data = await TemplateApi.getTemplate(parseInt(templateId))
        if (data.formSchema) {
            const schema = JSON.parse(data.formSchema)
            templateRule.value = schema.rule || []
            templateOption.value = {
                ...templateOption.value,
                ...schema.option
            }
        }
    } catch (error) {
        console.error('加载模板失败:', error)
        message.error('加载模板失败')
    }
}

// 加载评估基础信息
const loadEvalInfo = () => {
    try {
        const query = route.query
        evalInfo.elderId = parseInt(query.elderId as string)
        evalInfo.elderName = query.elderName as string
        evalInfo.templateId = parseInt(query.templateId as string)
        evalInfo.templateName = query.templateName as string
        evalInfo.evaluatorId = parseInt(query.evaluatorId as string)
        evalInfo.evaluatorName = query.evaluatorName as string
        evalInfo.evaluationTime = query.evaluationTime
    } catch (error) {
        console.error('加载评估信息失败:', error)
        message.error('加载评估信息失败')
    }
}

// 处理表单提交
const handleSubmit = async (formData: any) => {
    // 获取复制的规则
    const copyRules = formCreate.copyRules(templateRule.value)
    const options = templateOption.value


    // 收集评估结果
    const assessmentResults: any[] = []
    

    // 递归处理规则和收集结果
    const processRules = (rules: any[]) => {
        rules.forEach(rule => {
            if (rule._fc_drag_tag === 'elCard') {
                // 如果是卡片，收集卡片标题和子项的值
                const cardResult = {
                    title: rule.props.header,
                    items: [] as any[],
                    type: rule.type
                }

                // 处理卡片的子项
                rule.children.forEach((child: any) => {
                    // 检查子项是否有值
                    if (child.value) {
                        cardResult.items.push({
                            type: child.type,
                            name: child.name,
                            info: child.info,
                            title: child.title,
                            props: child.props,
                            options: child.options,
                            value: child.value
                        })
                    } else if (formData[child.field]) {
                        // 如果子项本身没有值，但在formData中有值
                        child.value = formData[child.field]
                        cardResult.items.push({
                            type: child.type,
                            name: child.name,
                            info: child.info,
                            title: child.title,
                            props: child.props,
                            options: child.options,
                            value: formData[child.field]
                        })
                    }
                })

                if (cardResult.items.length > 0) {
                    assessmentResults.push(cardResult)
                }
            } else if (rule._fc_drag_tag !== 'elAlert') {
                // 处理其他类型的组件
                const otherResult = {
                    title: rule.title || rule.name, // 使用 title 或 name 作为标题
                    items: [] as any[],
                    type: rule.type
                };

                // 处理子项
                if (rule.children && rule.children.length > 0) {
                    rule.children.forEach((child: any) => {
                        const childValue = child.value || formData[child.field];
                        if (childValue !== undefined) {
                            otherResult.items.push({
                                type: child.type,
                                name: child.name,
                                info: child.info || '',
                                title: child.title || '',
                                props: child.props,
                                options: child.options,
                                value: childValue
                            });
                        }
                    });
                } else {
                    // 如果没有子项，直接处理当前组件
                    const childValue = rule.value || formData[rule.field];
                    if (childValue !== undefined) {
                        otherResult.items.push({
                            type: rule.type,
                            name: rule.name,
                            info: rule.info || '',
                            title: rule.title || '',
                            props: rule.props,
                            options: rule.options,
                            value: childValue
                        });
                    }
                }

                if (otherResult.items.length > 0) {
                    assessmentResults.push(otherResult);
                }
            }
        })
    }

    // const processAllRules = (rules: any[]) => {
    //     rules.forEach(rule => {
    //         if (rule._fc_drag_tag === 'elCard') {
    //             // 如果是卡片，收集卡片标题和子项的值
    //             const cardResult = {
    //                 title: rule.props.header,
    //                 items: [] as any[]
    //             }

    //             // 处理卡片的子项
    //             rule.children.forEach((child: any) => {


    //                 cardResult.items.push({
    //                     name: child.name,
    //                     info: child.info,
    //                     props: child.props,
    //                     options: child.options,
    //                     value: child.value
    //                 })

    //             })

    //             if (cardResult.items.length > 0) {
    //                 assessmentDetailResults.push(cardResult)
    //             }
    //         }
    //     })
    // }

    // 处理所有规则
    processRules(copyRules)
    // processAllRules(copyRules)

    try {
        let aiInput = `评估表标题：${options.formName}\n\n`;

        // 遍历评估结果
        assessmentResults.forEach(result => {
            // 使用 result.title 作为一级问题
            const primaryQuestion = `一级问题：${result.title ? result.title : result.info ? result.info : result.name}\n`;

            // 遍历每个问题的项
            result.items.forEach(item => {
                // 如果是卡片类型，显示二级问题
                if (result.type === 'elCard') {
                    aiInput += primaryQuestion; // 在每个二级问题前显示一级问题
                    aiInput += `二级问题：${item.title ? item.title : item.info ? item.info : item.name}\n`; // 使用 item.title 或 item.name 作为二级问题
                } else {
                    // 对于非卡片类型，直接显示一级问题
                    aiInput += primaryQuestion; // 显示一级问题
                }

                let resultValue = item.value; // 默认使用 item.value
                if (item.options && item.options.length > 0) {
                    // 如果是单选或其他类型，直接获取对应的 label
                    const option = item.options.find(opt => opt.value === item.value);
                    const label = option ? option.label : '未找到对应的标签';
                    resultValue = label; // 如果找到对应的 label，则使用它
                } if (Array.isArray(item.value)) {

                    // 如果是多选框，处理数组值
                    const labels = item.value.map(val => {
                        const option = item.options.find(opt => opt.value === val);
                        return option ? option.label : '未找到对应的标签';
                    });
                    resultValue = labels.length > 0 ? labels.join(', ') : '未找到对应的标签'; // 将标签用逗号连接
                }
                aiInput += `评估结果：${resultValue}\n\n`;
            
            });
        });

        // 输出结果
        console.log(aiInput);

        // 创建评估结果
        await ResultApi.createResult({
            elderId: evalInfo.elderId,
            elderName: evalInfo.elderName,
            templateId: evalInfo.templateId,
            templateName: evalInfo.templateName,
            evaluatorId: evalInfo.evaluatorId,
            evaluatorName: evalInfo.evaluatorName,
            evaluationTime: evalInfo.evaluationTime,
            aiInputs: aiInput,
            aiAnalysis: '',
            evaluatorAnalysis: '',
            result: JSON.stringify({
                options: options,
                rules: copyRules,              // 原始规则
                assessmentResults: assessmentResults,  // 评估结果
                // assessmentDetailResults: assessmentDetailResults  // 评估结果
            })
        })

        

        message.success('保存成功')
        // 保存成功后返回列表页
        router.push('/evaluation/elder-eval')
    } catch (error) {
        console.error('保存评估结果失败:', error)
        message.error('保存失败')
    }
}

// 初始化
onMounted(() => {
    loadTemplate()
    loadEvalInfo()
})
</script>

<template>
    <ContentWrap>
        <div class="mb-4">
            <el-descriptions :column="3" border>
                <el-descriptions-item label="老人姓名">{{ evalInfo.elderName }}</el-descriptions-item>
                <el-descriptions-item label="评估师">{{ evalInfo.evaluatorName }}</el-descriptions-item>
                <el-descriptions-item label="评估时间">{{ evalInfo.evaluationTime }}</el-descriptions-item>
            </el-descriptions>
        </div>
        <div class="evaluation-form">
            <form-create v-model="previewForm" v-model:api="previewApi" :rule="templateRule" :option="templateOption"
                @submit="handleSubmit" />
        </div>
    </ContentWrap>
</template>

<style lang="scss" scoped>
.evaluation-form {
    padding: 20px;
    background: #fff;
    border-radius: 4px;

    :deep(.el-form) {
        max-width: 800px;
        margin: 0 auto;
    }
}
</style>
