import request from '@/config/axios'

// 老人信息扩展 VO
export interface ArchivesExtensionVO {
  id: number // 扩展信息ID
  elderId: number // 关联的老人ID
  selfCareAbility: number // 自理能力(1-完全自理 2-轻度依赖 3-中度依赖 4-重度依赖)
  mentalState: number // 精神状态(1-正常 2-轻度认知障碍 3-中重度认知障碍)
  physicalDisease: string // 躯体疾病(多选,以逗号分隔:1-高血压 2-心脏病 3-糖尿病 4-慢性支气管炎 5-恶性肿瘤 6-骨折 7-关节炎 8-其他慢性病)
  mentalDisease: string // 精神疾病(多选,以逗号分隔:1-焦虑 2-抑郁 3-失眠 4-认知障碍 5-其他)
  medicationStatus: string // 服药情况
  fallHistory: number // 跌倒史(0-无 1-1次 2-2次 3-3次及以上)
  wanderingHistory: number // 走失史(0-无 1-1次 2-2次 3-3次及以上)
  hospitalizationHistory: number // 住院史(0-无 1-1次 2-2次 3-3次及以上)
  otherAccidents: string // 其他意外事件
  recordDate: Date // 记录日期
  recorder: string // 记录人
  remark: string // 备注
  createTime: Date // 创建时间
  updateTime: Date // 更新时间
  creator: string // 创建人
  updater: string // 更新人
}

// 创建请求参数
export interface ArchivesExtensionCreateReqVO {
  elderId: number
  selfCareAbility: number
  mentalState: number
  physicalDisease: string
  mentalDisease: string
  medicationStatus: string
  fallHistory: number
  wanderingHistory: number
  hospitalizationHistory: number
  otherAccidents: string
  remark: string
}

// 分页查询参数
export interface ArchivesExtensionPageReqVO {
  elderId?: number
  pageNo?: number
  pageSize?: number
}

// 老人信息扩展 API
export const ArchivesExtensionApi = {
  // 查询老人信息扩展分页
  getArchivesExtensionPage: async (params: ArchivesExtensionPageReqVO) => {
    return await request.get({ url: `/elderArchives/archives-extension/page`, params })
  },

  // 查询老人信息扩展详情
  getArchivesExtension: async (id: number) => {
    return await request.get({ url: `/elderArchives/archives-extension/get?id=` + id })
  },

  // 新增老人信息扩展
  createArchivesExtension: async (data: ArchivesExtensionCreateReqVO) => {
    return await request.post({ url: `/elderArchives/archives-extension/create`, data })
  },

  // 修改老人信息扩展
  updateArchivesExtension: async (data: ArchivesExtensionVO) => {
    return await request.put({ url: `/elderArchives/archives-extension/update`, data })
  },

  // 删除老人信息扩展
  deleteArchivesExtension: async (id: number) => {
    return await request.delete({ url: `/elderArchives/archives-extension/delete?id=` + id })
  },

  // 导出老人信息扩展 Excel
  exportArchivesExtension: async (params: ArchivesExtensionPageReqVO) => {
    return await request.download({ url: `/elderArchives/archives-extension/export-excel`, params })
  },
}
