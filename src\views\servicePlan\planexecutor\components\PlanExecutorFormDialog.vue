<template>
  <Dialog :title="title" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="关联服务计划" prop="planId">
        <el-select
          v-model="formData.planId"
          placeholder="请选择服务计划"
          filterable
          clearable
          :disabled="props.mode === 'conflict'"
        >
          <el-option
            v-for="plan in planList"
            :key="plan.id"
            :label="plan.planName"
            :value="plan.id"
          >
            <span>{{ plan.planName }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="formData.executorId"
          placeholder="请选择执行人"
          filterable
          clearable
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          >
            <span>{{ user.nickname }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-select
          v-model="formData.elderId"
          placeholder="请选择老人"
          filterable
          clearable
          :disabled="props.mode === 'conflict'"
        >
          <el-option
            v-for="elder in elderList"
            :key="elder.id"
            :label="elder.name"
            :value="elder.id"
          >
            <span>{{ elder.name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联任务ID" prop="taskId">
        <el-input v-model="formData.taskId" :disabled="props.mode === 'conflict'" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="datetime"
          value-format="x"
          format="YYYY-MM-DD HH:mm"
          placeholder="开始时间"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="formData.endTime"
          type="datetime"
          value-format="x"
          format="YYYY-MM-DD HH:mm"
          placeholder="结束时间"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="实际开始时间" prop="actualStart" v-if="props.mode !== 'conflict'">
        <el-date-picker
          v-model="formData.actualStart"
          type="datetime"
          value-format="x"
          format="YYYY-MM-DD HH:mm"
          placeholder="选择实际开始时间"
        />
      </el-form-item>
      <el-form-item label="实际结束时间" prop="actualEnd" v-if="props.mode !== 'conflict'">
        <el-date-picker
          v-model="formData.actualEnd"
          type="datetime"
          value-format="x"
          format="YYYY-MM-DD HH:mm"
          placeholder="选择实际结束时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex items-center justify-between">
        <div>
          <el-button
            type="warning"
            :disabled="!canCheckElderConflict"
            @click="checkElderConflict"
          >
            检测老人冲突
          </el-button>
          <el-button
            type="warning"
            :disabled="!canCheckExecutorConflict"
            @click="checkExecutorConflict"
          >
            检测执行人冲突
          </el-button>
        </div>
        <div>
          <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { PlanExecutorApi, PlanExecutorVO } from '@/api/servicePlan/planexecutor'
import { ConflictLogApi } from '@/api/servicePlan/serviceplanconflictlog'
import { getSimpleUserList } from '@/api/system/user'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ServicePlanApi } from '@/api/servicePlan/serviceplan'

import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({ name: 'PlanExecutorFormDialog' })

interface Props {
  mode?: 'edit' | 'conflict'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit'
})

const title = computed(() => props.mode === 'conflict' ? '解决时间冲突' : '编辑任务执行人')

const message = useMessage()
const dialogVisible = ref(false)
const formLoading = ref(false)

interface FormData extends Partial<PlanExecutorVO> {
  startTime?: number
  endTime?: number
  elderId?: number
}

const formData = ref<FormData>({
  id: undefined,
  taskId: undefined,
  executorId: undefined,
  elderId: undefined,
  planId: undefined,
  startTime: undefined,
  endTime: undefined,
  actualStart: undefined,
  actualEnd: undefined
})

const formRules = reactive({
  taskId: [{ required: true, message: '关联任务ID不能为空', trigger: 'blur' }],
  executorId: [{ required: true, message: '执行人ID不能为空', trigger: 'blur' }],
  elderId: [{ required: true, message: '请选择关联老人', trigger: 'change' }],
  planId: [{ required: true, message: '关联服务计划ID不能为空', trigger: 'blur' }],
  startTime: [{ required: true, message: '开始时间不能为空', trigger: 'change' }],
  endTime: [
    { required: true, message: '结束时间不能为空', trigger: 'change' },
    {
      validator: (_: any, value: string, callback: Function) => {
        if (!value || !formData.value.startTime) {
          callback()
          return
        }
        if (value <= formData.value.startTime) {
          callback(new Error('结束时间必须大于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
})

const formRef = ref()
const emit = defineEmits(['success'])

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  await getElderList()
  await getPlanList()
  resetForm()
  
  if (id) {
    formLoading.value = true
    try {
      const data = await PlanExecutorApi.getPlanExecutor(id)
      formData.value = {
        ...data,
        startTime: data.startTime,
        endTime: data.endTime
      }
      console.log('加载的数据:', formData.value)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open })

/** 检查所有冲突 */
const checkAllConflicts = async () => {
  if (!canCheckElderConflict.value || !canCheckExecutorConflict.value) {
    ElMessage.warning('请填写完整的执行时间和相关ID')
    return false
  }

  try {
    // 检查老人冲突
    const elderResponse = await PlanExecutorApi.checkElderTimeConflict({
      taskId: formData.value.taskId!,
      elderId: formData.value.elderId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    // 检查执行人冲突
    const executorResponse = await PlanExecutorApi.checkExecutorTimeConflict({
      id: formData.value.id,
      executorId: formData.value.executorId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    elderConflicts.value = elderResponse
    executorConflicts.value = executorResponse

    // 如果有冲突，显示确认对话框
    if (elderResponse.length > 0 || executorResponse.length > 0) {
      const confirmMessage = [
        '检测到以下冲突：',
        elderResponse.length > 0 ? `- ${elderResponse.length}个老人时间冲突` : '',
        executorResponse.length > 0 ? `- ${executorResponse.length}个执行人时间冲突` : '',
        '是否仍要继续保存？'
      ].filter(Boolean).join('\n')

      try {
        await ElMessageBox.confirm(confirmMessage, '时间冲突提醒', {
          type: 'warning',
          confirmButtonText: '继续保存',
          cancelButtonText: '取消'
        })
        return true // 用户确认继续
      } catch {
        return false // 用户取消
      }
    }else{
      // 如果是冲突解决模式，更新冲突状态
    if (props.mode === 'conflict') {
      console.log('更新冲突状态')
      await ConflictLogApi.updateConflictLogStatus({
        
        taskExecutorId: formData.value.id!,
        resolveStatus: 2, // 已解决状态
        resolveComment: '时间冲突已解决'
      })
    }
    }

    return true // 没有冲突
  } catch (error) {
    console.error('检测冲突失败:', error)
    ElMessage.error('检测冲突失败')
    return false
  }
}

/** 提交表单 */
const submitForm = async () => {
  await formRef.value.validate()
  
  // 先检测冲突
  const canContinue = await checkAllConflicts()
  if (!canContinue) {
    return
  }

  formLoading.value = true
  try {
    const submitData: PlanExecutorVO = {
      ...formData.value,
      startTime: formData.value.startTime,
      endTime: formData.value.endTime
    }
    console.log('提交的数据:', submitData)
    await PlanExecutorApi.updatePlanExecutor(submitData)
    
    
    
    message.success('修改成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    formLoading.value = false
  }
}

// 冲突状态
const elderConflicts = ref<any[]>([])
const executorConflicts = ref<any[]>([])

// 判断是否可以检测冲突
const canCheckElderConflict = computed(() => {
  return formData.value.elderId && 
         formData.value.startTime &&
         formData.value.endTime
})

const canCheckExecutorConflict = computed(() => {
  return formData.value.executorId && 
         formData.value.startTime &&
         formData.value.endTime
})

// 检测老人时间冲突
const checkElderConflict = async () => {
  try {
    const response = await PlanExecutorApi.checkElderTimeConflict({
      taskId: formData.value.taskId!,
      elderId: formData.value.elderId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    elderConflicts.value = response
    if (response.length > 0) {
      ElMessage.warning(`发现 ${response.length} 个老人时间冲突`)
    } else {
      ElMessage.success('未发现老人时间冲突')
    }
  } catch (error) {
    console.error('检测老人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

// 检测执行人时间冲突
const checkExecutorConflict = async () => {
  try {
    const response = await PlanExecutorApi.checkExecutorTimeConflict({
      id: formData.value.id,
      executorId: formData.value.executorId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    executorConflicts.value = response
    if (response.length > 0) {
      ElMessage.warning(`发现 ${response.length} 个执行人时间冲突`)
    } else {
      ElMessage.success('未发现执行人时间冲突')
    }
  } catch (error) {
    console.error('检测执行人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskId: undefined,
    executorId: undefined,
    elderId: undefined,
    planId: undefined,
    startTime: undefined,
    endTime: undefined,
    actualStart: undefined,
    actualEnd: undefined
  }
  formRef.value?.resetFields()
  elderConflicts.value = []
  executorConflicts.value = []
}

// 监听开始时间变化，自动调整结束时间
watch(() => formData.value.startTime, (newStartTime) => {
  if (newStartTime && formData.value.endTime && formData.value.endTime <= newStartTime) {
    formData.value.endTime = undefined
  }
})

// 添加用户列表
const userList = ref([])

// 获取用户列表
const getUserList = async () => {
  try {
    const data = await getSimpleUserList()
    userList.value = data
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 计划列表
const planList = ref([])

// 获取计划列表
const getPlanList = async () => {
  try {
    const data = await ServicePlanApi.getServicePlanSimpleList()
    planList.value = data
  } catch (error) {
    console.error('获取计划列表失败:', error)
    message.error('获取计划列表失败')
  }
}

// 在 onMounted 中调用
onMounted(() => {
  getUserList()
  getElderList()
  getPlanList()
})

// 在 script setup 中添加老人列表相关代码
const elderList = ref([])

// 获取老人列表
const getElderList = async () => {
  try {
    const data = await ArchivesProfileApi.getArchivesProfileSimpleList()
    elderList.value = data
  } catch (error) {
    console.error('获取老人列表失败:', error)
    message.error('获取老人列表失败')
  }
}
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.mr-2 {
  margin-right: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 
