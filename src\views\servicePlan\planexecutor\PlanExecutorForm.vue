<template>
  <div>
    <PlanExecutorFormDialog
      ref="formRef"
      mode="edit"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
// 使用相对路径导入
import PlanExecutorFormDialog from './components/PlanExecutorFormDialog.vue'

/** 任务执行人关联 表单 */
defineOptions({ name: 'PlanExecutorForm' })

const formRef = ref()
const emit = defineEmits(['success'])

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  formRef.value?.open(id)
}
defineExpose({ open })

/** 处理成功 */
const handleSuccess = () => {
  emit('success')
}
</script>