import request from '@/config/axios'
import type { ArchivesProfileVO } from '../archivesProfile'

// 老人信息页面 API
export const ArchivesInfoApi = {
  // 查询老人信息分页
  getArchivesInfoPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/archives-info/page`, params })
  },

  // 查询老人信息详情
  getArchivesInfo: async (id: number) => {
    return await request.get({ url: `/elderArchives/archives-info/get?id=` + id })
  },

  // 导出老人信息 Excel
  exportArchivesInfo: async (params) => {
    return await request.download({ url: `/elderArchives/archives-info/export-excel`, params })
  }
}
