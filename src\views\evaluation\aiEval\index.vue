<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile/index'
import { TemplateApi } from '@/api/evaluation/template'
import { ResultApi } from '@/api/evaluation/result'
import formCreate from '@form-create/element-ui'
import { formatDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'

defineOptions({ name: 'AiEvalIndex' })

// 消息弹窗
const message = useMessage()
// 路由
const route = useRoute()
// 路由
const router = useRouter()

// 预览表单
const previewForm = ref({})
// 预览API
const previewApi = ref(null)
// 模板规则
const templateRule = ref([])
// 模板选项
const templateOption = ref({
  submitBtn: { show: true },
  resetBtn: { show: true }
})
// 评估师分析
const evaluatorAnalysis = ref<string>('')

// 老人信息
const elderInfo = ref<ArchivesProfileVO | null>(null)

// 评估基础信息
const evalInfo = reactive({
  elderId: undefined as number | undefined,
  elderName: '',
  templateId: undefined as number | undefined,
  templateName: '',
  evaluatorId: undefined as number | undefined,
  evaluatorName: '',
  evaluationTime: '' as any, // 允许字符串或日期类型
  evaluationReason: '',
  type: 0 as number
})

// 添加模板类型的响应式变量
const templateType = ref<number>(0)

// 添加模板类型选项
const templateTypeOptions = ref<Array<any>>([])

// 获取表单类型名称的计算属性
const getTemplateTypeName = computed(() => {
  const option = templateTypeOptions.value.find((item: any) => item.value === templateType.value)
  return option ? option.label : '未知类型'
})

// 添加模板详情变量
const templateDetail = ref({
  version: '',
  validityPeriod: 3,
  validityUnit: 'month',
  validityStartTimeType: 'evaluationTime',
  validityStartTime: ''
})

// 加载模板数据
const loadTemplate = async () => {
  const templateId = route.query.templateId as string
  if (!templateId) return

  try {
    // 获取模板详情
    const data = await TemplateApi.getTemplate(parseInt(templateId))
    templateDetail.value = data

    // 解析formSchema提取有效期设置
    if (data.formSchema) {
      try {
        const schema = JSON.parse(data.formSchema)
        // console.log(schema)
        if (schema.option && schema.option.form) {
          templateDetail.value.validityPeriod = schema.option.form.validityPeriod || 3
          templateDetail.value.validityUnit = schema.option.form.validityUnit || 'month'
          templateDetail.value.validityStartTimeType =
            schema.option.form.validityStartTimeType || 'evaluationTime'
          templateDetail.value.validityStartTime = schema.option.form.validityStartTime || ''
        }
      } catch (e) {
        // console.error('解析模板数据失败:', e)
        ElMessage.error('解析模板数据失败')
      }
    }

    // 其他模板加载逻辑
    if (data.formSchema) {
      const schema = JSON.parse(data.formSchema)
      templateRule.value = schema.rule || []

      // 保存完整的原始模板选项
      templateOption.value = {
        ...templateOption.value,
        ...schema.option,
        formName: schema.option?.formName || evalInfo.templateName, // 确保formName可用
        form: schema.option?.form || {}
      }

      // 获取模板类型
      templateType.value = schema.option?.form?.templateType || 0
    }
  } catch (error) {
    // console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  }
}

// 加载评估基础信息
const loadEvalInfo = async () => {
  try {
    const query = route.query
    evalInfo.elderId = parseInt(query.elderId as string)
    evalInfo.elderName = query.elderName as string
    evalInfo.templateId = parseInt(query.templateId as string)
    evalInfo.templateName = query.templateName as string
    evalInfo.evaluatorId = parseInt(query.evaluatorId as string)
    evalInfo.evaluatorName = query.evaluatorName as string
    evalInfo.evaluationTime = query.evaluationTime
    evalInfo.evaluationReason = query.evaluationReason as string
    evalInfo.type = query.type as unknown as number
    // console.log(evalInfo.evaluationReason)
    // 根据 elderId 获取老人信息
    if (evalInfo.elderId) {
      const elderData = await ArchivesProfileApi.getArchivesProfile(evalInfo.elderId)
      elderInfo.value = elderData // 存储老人信息
    }
    console.log(elderInfo.value)
  } catch (error) {
    // console.error('加载评估信息失败:', error)
    message.error('加载评估信息失败')
  }
}

// 处理表单提交
const handleSubmit = async (formData: any) => {
  // console.log('提交的表单数据:', formData)
  // 复制规则以避免修改原始数据
  const copyRules = formCreate.copyRules(templateRule.value)
  const options = templateOption.value

  try {
    let aiInput = `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 构建字段和标题的映射关系
    const fieldTitleMap = {}
    const fieldOptionsMap = {}
    const fieldParentMap = {}
    const fieldMidParentMap = {}
    const fieldTopParentMap = {}

    // 递归函数，用于从规则中提取字段信息
    const extractFieldInfo = (
      rules,
      parentTitle = '',
      midParentTitle = '',
      topParentTitle = ''
    ) => {
      if (!rules || !Array.isArray(rules)) return

      rules.forEach((rule) => {
        // 跳过elAlert类型
        if (rule._fc_drag_tag === 'elAlert') return

        // 获取当前项的标题
        let currentTitle = ''
        if (rule.props?.header) {
          currentTitle = rule.props.header
        } else if (rule.title && rule.title.trim() !== '') {
          currentTitle = rule.title
        } else if (rule.name?.startsWith('ref_') && rule.info && rule.info.trim() !== '') {
          currentTitle = rule.info
        } else if (rule.name && !rule.name.startsWith('ref_')) {
          currentTitle = rule.name
        }

        // 记录规则的基本信息
        // if (rule.field) {
        //   console.log(
        //     `提取字段信息: 字段=${rule.field}, 标题=${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // } else if (currentTitle) {
        //   console.log(
        //     `处理标题: ${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // }

        // 如果有字段，记录它的信息
        if (rule.field) {
          fieldTitleMap[rule.field] = currentTitle || ''
          fieldParentMap[rule.field] = parentTitle || ''
          fieldMidParentMap[rule.field] = midParentTitle || ''
          fieldTopParentMap[rule.field] = topParentTitle || ''

          if (rule.options && rule.options.length > 0) {
            fieldOptionsMap[rule.field] = rule.options
            // console.log(`字段${rule.field}的选项:`, rule.options)
          }
        }

        // 如果有子项，递归处理
        if (rule.children && rule.children.length > 0) {
          // 确定标题的层级关系
          let newTopParent = topParentTitle
          let newMidParent = midParentTitle

          // 如果是顶层卡片
          if (!parentTitle && rule._fc_drag_tag === 'elCard') {
            newTopParent = currentTitle
          }
          // 如果已有顶层父级，但没有中间父级
          else if (topParentTitle && !midParentTitle && rule._fc_drag_tag === 'elCard') {
            newMidParent = currentTitle
          }

          extractFieldInfo(rule.children, currentTitle || parentTitle, newMidParent, newTopParent)
        }
      })
    }

    // 提取字段信息
    // console.log('开始提取字段信息...')
    extractFieldInfo(copyRules)
    // console.log('字段标题映射:', fieldTitleMap)
    // console.log('字段父标题映射:', fieldParentMap)
    // console.log('字段中间父标题映射:', fieldMidParentMap)
    // console.log('字段顶层标题映射:', fieldTopParentMap)
    // console.log('字段选项映射:', fieldOptionsMap)

    // 处理formData，构建aiInput
    // console.log('开始构建aiInput...')

    // 将评估结果组织成层级结构，使用数组保持顺序
    const hierarchicalResults = {}
    // 记录处理顺序
    const processOrder = {
      topParents: [] as any[],
      midParents: {},
      parentTitles: {},
      fields: {}
    }

    // 对字段进行分组和层级化处理
    for (const field in formData) {
      if (fieldTopParentMap[field]) {
        const topParent = fieldTopParentMap[field]
        const midParent = fieldMidParentMap[field]
        const parentTitle = fieldParentMap[field]
        const fieldTitle = fieldTitleMap[field]

        // 记录处理顺序
        // 记录顶层标题顺序
        if (!processOrder.topParents.includes(topParent)) {
          processOrder.topParents.push(topParent)
        }

        // 记录中间标题顺序
        if (midParent && midParent !== topParent) {
          if (!processOrder.midParents[topParent]) {
            processOrder.midParents[topParent] = []
          }
          if (!processOrder.midParents[topParent].includes(midParent)) {
            processOrder.midParents[topParent].push(midParent)
          }

          // 记录直接父标题顺序（在中间标题下）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.parentTitles[midKey]) {
              processOrder.parentTitles[midKey] = []
            }
            if (!processOrder.parentTitles[midKey].includes(parentTitle)) {
              processOrder.parentTitles[midKey].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${midParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在中间标题下）
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.fields[midKey]) {
              processOrder.fields[midKey] = []
            }
            processOrder.fields[midKey].push(field)
          }
        } else {
          // 记录直接父标题顺序（在顶层标题下）
          if (parentTitle && parentTitle !== topParent) {
            if (!processOrder.parentTitles[topParent]) {
              processOrder.parentTitles[topParent] = []
            }
            if (!processOrder.parentTitles[topParent].includes(parentTitle)) {
              processOrder.parentTitles[topParent].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在顶层标题下）
            if (!processOrder.fields[topParent]) {
              processOrder.fields[topParent] = []
            }
            processOrder.fields[topParent].push(field)
          }
        }

        // 处理值
        let resultValue = formData[field]
        let resultLabel = ''
        if (fieldOptionsMap[field]) {
          if (Array.isArray(resultValue)) {
            const labels = resultValue.map((val) => {
              const option = fieldOptionsMap[field].find((opt) => String(opt.value) === String(val))
              return option ? option.label : '未找到标签'
            })
            resultLabel = labels.join('、')
          } else {
            const option = fieldOptionsMap[field].find(
              (opt) => String(opt.value) === String(resultValue)
            )
            resultLabel = option ? option.label : '未找到标签'
          }
        } else {
          resultLabel = String(resultValue)
        }

        // 创建层级结构
        if (!hierarchicalResults[topParent]) {
          hierarchicalResults[topParent] = {
            title: topParent,
            children: [],
            childrenMap: {} // 用于快速查找
          }
        }

        // 添加中间父级（如果存在）
        if (midParent && midParent !== topParent) {
          // 检查中间父级是否已存在
          if (!hierarchicalResults[topParent].childrenMap[midParent]) {
            const midParentNode = {
              title: midParent,
              parent: topParent,
              children: [],
              childrenMap: {} // 用于快速查找
            }
            hierarchicalResults[topParent].children.push(midParentNode)
            hierarchicalResults[topParent].childrenMap[midParent] = midParentNode
          }

          // 添加直接父级（如果存在且与中间父级不同）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            // 检查直接父级是否已存在
            if (!midParentNode.childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: midParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              midParentNode.children.push(parentNode)
              midParentNode.childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = midParentNode.childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与中间父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: midParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            midParentNode.children.push(fieldNode)
            midParentNode.childrenMap[fieldKey] = fieldNode
          }
        }
        // 如果没有中间父级
        else {
          // 添加直接父级（如果存在且与顶层父级不同）
          if (parentTitle && parentTitle !== topParent) {
            // 检查直接父级是否已存在
            if (!hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: topParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              hierarchicalResults[topParent].children.push(parentNode)
              hierarchicalResults[topParent].childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与顶层父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: topParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            hierarchicalResults[topParent].children.push(fieldNode)
            hierarchicalResults[topParent].childrenMap[fieldKey] = fieldNode
          }
        }

        // 构建文本输出
        aiInput += `${topParent}\n`
        if (midParent && midParent !== topParent) {
          aiInput += `${midParent}\n`
        }
        if (parentTitle && parentTitle !== topParent && parentTitle !== midParent) {
          aiInput += `${parentTitle}\n`
        }
        if (
          fieldTitle &&
          fieldTitle !== parentTitle &&
          fieldTitle !== midParent &&
          fieldTitle !== topParent
        ) {
          aiInput += `${fieldTitle}\n`
        }
        aiInput += `${resultLabel}\n\n`
      }
    }

    // 最终处理，根据处理顺序重新组织结果，并删除临时的childrenMap
    const orderedResults = {}
    processOrder.topParents.forEach((topParent) => {
      if (hierarchicalResults[topParent]) {
        // 创建有序的顶级结果
        const topResult = {
          title: hierarchicalResults[topParent].title,
          children: [] as any[]
        }

        // 处理中间父级（如果有）
        if (processOrder.midParents[topParent]) {
          processOrder.midParents[topParent].forEach((midParent) => {
            if (hierarchicalResults[topParent].childrenMap[midParent]) {
              const midNode = hierarchicalResults[topParent].childrenMap[midParent]
              const orderedMidNode = {
                title: midNode.title,
                parent: midNode.parent,
                children: [] as any[]
              }

              // 处理直接父级（如果有）
              const midKey = `${topParent}:${midParent}`
              if (processOrder.parentTitles[midKey]) {
                processOrder.parentTitles[midKey].forEach((parentTitle) => {
                  if (midNode.childrenMap[parentTitle]) {
                    const parentNode = midNode.childrenMap[parentTitle]
                    const orderedParentNode = {
                      title: parentNode.title,
                      parent: parentNode.parent,
                      children: [] as any[]
                    }

                    // 处理字段
                    const parentKey = `${topParent}:${midParent}:${parentTitle}`
                    if (processOrder.fields[parentKey]) {
                      processOrder.fields[parentKey].forEach((field) => {
                        const fieldKey = fieldTitleMap[field] || field
                        if (parentNode.childrenMap[fieldKey]) {
                          orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                        }
                      })
                    }

                    orderedMidNode.children.push(orderedParentNode)
                  }
                })
              }

              // 处理直接在中间父级下的字段
              if (processOrder.fields[midKey]) {
                processOrder.fields[midKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (midNode.childrenMap[fieldKey] && !midNode.childrenMap[fieldKey].children) {
                    orderedMidNode.children.push(midNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedMidNode)
            }
          })
        }

        // 处理直接父级（如果没有中间父级）
        if (processOrder.parentTitles[topParent]) {
          processOrder.parentTitles[topParent].forEach((parentTitle) => {
            if (hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
              const orderedParentNode = {
                title: parentNode.title,
                parent: parentNode.parent,
                children: [] as any[]
              }

              // 处理字段
              const parentKey = `${topParent}:${parentTitle}`
              if (processOrder.fields[parentKey]) {
                processOrder.fields[parentKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (parentNode.childrenMap[fieldKey]) {
                    orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedParentNode)
            }
          })
        }

        // 处理直接在顶级下的字段
        if (processOrder.fields[topParent]) {
          processOrder.fields[topParent].forEach((field) => {
            const fieldKey = fieldTitleMap[field] || field
            if (
              hierarchicalResults[topParent].childrenMap[fieldKey] &&
              !hierarchicalResults[topParent].childrenMap[fieldKey].children
            ) {
              topResult.children.push(hierarchicalResults[topParent].childrenMap[fieldKey])
            }
          })
        }

        orderedResults[topParent] = topResult
      }
    })

    // console.log('原始层级化结果:', hierarchicalResults)
    // console.log('处理顺序:', processOrder)
    // console.log('有序层级化结果:', orderedResults)
    // console.log('最终AI输入:\n', aiInput)

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evalInfo.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value ? new Date().getFullYear() - elderInfo.value.birthDate[0] : ''
        },
        templateInfo: {
          id: evalInfo.templateId || 0,
          name: evalInfo.templateName,
          type: templateType.value,
          validityPeriod: templateDetail.value?.validityPeriod || 3,
          validityUnit: templateDetail.value?.validityUnit || 'month',
          validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
          validityStartTime: templateDetail.value?.validityStartTime || ''
        },
        evaluationInfo: {
          evaluatorId: evalInfo.evaluatorId || 0,
          evaluatorName: evalInfo.evaluatorName,
          evaluationReason: evalInfo.evaluationReason,
          evaluationTime: evalInfo.evaluationTime
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInput,
      hierarchicalResults: orderedResults,
      rawFormData: formData
    }

    // 将完整结果转换为JSON字符串
    // const fullResultJSONString = JSON.stringify(fullResultJSON, null, 2)
    // console.log('完整JSON结果:', fullResultJSONString)

    // if (templateType.value !== 0) {
    //   if (!evaluatorAnalysis.value) {
    //     message.error('评估师分析不能为空')
    //     return
    //   }
    // }

    // 创建评估结果
    await ResultApi.createResult({
      elderId: evalInfo.elderId || 0,
      elderName: elderInfo.value?.name || '',
      templateId: evalInfo.templateId || 0,
      templateName: evalInfo.templateName,
      evaluatorId: evalInfo.evaluatorId || 0,
      evaluatorName: evalInfo.evaluatorName,
      evaluationReason: evalInfo.evaluationReason,
      evaluationTime: new Date().getTime(),
      aiInputs: aiInput,
      aiAnalysis: '',
      type: evalInfo.type,
      evaluatorAnalysis: evaluatorAnalysis.value,
      result: JSON.stringify({
        options: options,
        rules: copyRules,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      })
    })

    message.success('保存成功')
    // 保存成功后返回列表页
    router.push('/evaluation/result')
  } catch (error) {
    // console.error('保存评估结果失败:', error)
    message.error('保存失败')
  }
}

// 获取有效期单位文本
const getValidityUnitText = (unit) => {
  switch (unit) {
    case 'day':
      return '天'
    case 'week':
      return '周'
    case 'month':
    default:
      return '个月'
  }
}

// 添加计算剩余有效期的函数
const getRemainingValidity = () => {
  if (
    !templateDetail.value ||
    templateDetail.value.validityStartTimeType !== 'fixedDate' ||
    !templateDetail.value.validityStartTime
  ) {
    return null
  }

  try {
    // 获取固定起始日期
    const startDate = new Date(templateDetail.value.validityStartTime)
    // 获取有效期和单位
    const validityPeriod = templateDetail.value.validityPeriod || 3
    const validityUnit = templateDetail.value.validityUnit || 'month'

    // 计算到期日期
    let expiryDate = new Date(startDate)
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const today = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    return daysLeft > 0 ? daysLeft : 0
  } catch (error) {
    // console.error('计算剩余有效期失败:', error)
    ElMessage.error('计算剩余有效期失败')
    return null
  }
}

// 初始化
onMounted(() => {
  templateTypeOptions.value = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  loadTemplate()
  loadEvalInfo()
})

// 工具函数：脱敏处理
const maskString = (str: string, start: number, end: number) => {
  if (!str) return ''
  const maskLength = end - start
  const maskStr = '*'.repeat(maskLength)
  return str.substring(0, start) + maskStr + str.substring(end)
}

// 计算属性：脱敏后的身份证号
const maskedIdNumber = computed(() => {
  if (!elderInfo.value?.idNumber) return ''
  return maskString(elderInfo.value.idNumber, 6, 14)
})

// 计算属性：脱敏后的联系电话
const maskedPhone = computed(() => {
  if (!elderInfo.value?.contactPhone) return ''
  return maskString(elderInfo.value.contactPhone, 3, 7)
})
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="flex flex-1">
      <div class="flex-1 mr-4">
        <el-card class="min-h-83vh">
          <h2>{{ evalInfo.templateName }}</h2>
          <div class="text-gray-500 text-sm mt-2 mb-2">
            <span class="mr-4">表单类型: {{ getTemplateTypeName }}</span>
            <span class="mr-4">版本号: {{ templateDetail.version || '1.0.0' }}</span>
            <span>
              <template
                v-if="
                  templateDetail.validityStartTimeType === 'fixedDate' &&
                  templateDetail.validityStartTime
                "
              >
                剩余有效期: {{ getRemainingValidity() }} 天 (总有效期
                {{ templateDetail.validityPeriod || 3 }}
                {{ getValidityUnitText(templateDetail.validityUnit || 'month') }}，从
                {{ templateDetail.validityStartTime }}
                起)
              </template>
              <template v-else>
                有效期: {{ templateDetail.validityPeriod || 3 }}
                {{ getValidityUnitText(templateDetail.validityUnit || 'month') }} (从评估时间起)
              </template>
            </span>
          </div>
          <el-divider />
          <div class="evaluation-form" style="max-height: 69vh; overflow-y: auto">
            <form-create
              :modelValue="previewForm"
              v-model:api="previewApi"
              :rule="templateRule"
              :option="templateOption"
              @submit="handleSubmit"
            />
          </div>
        </el-card>
      </div>

      <!-- 老人信息 -->
      <el-card class="bg-white p-3 rounded-lg w-110 min-h-83vh">
        <!-- 评估信息 -->
        <div class="eval-info">
          <div class="info-item">
            <span class="label">评估师：</span>
            <span class="value">{{ evalInfo.evaluatorName }}</span>
          </div>
          <div class="info-item">
            <span class="label">评估原因：</span>
            <span class="value">{{ evalInfo.evaluationReason }}</span>
          </div>
        </div>
        <div class="flex flex-col items-center">
          <!-- <div class="w-24 h-24 rounded-full overflow-hidden mb-4">
            <img
              src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
              class="w-full h-full object-cover"
              alt="老人头像"
            />
          </div>
          <h3 class="text-lg font-medium">{{ elderName }}</h3> -->
        </div>
        <div class="mt-1 space-y-4">
          <div class="flex justify-between">
            <span>老人姓名:</span>
            <span>{{ elderInfo?.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>年龄:</span>
            <span>{{ elderInfo ? new Date().getFullYear() - elderInfo.birthDate[0] : '' }}岁</span>
          </div>
          <div class="flex justify-between">
            <span>身份证号:</span>
            <span>{{ maskedIdNumber }}</span>
          </div>
          <div class="flex justify-between">
            <span>性别:</span>
            <span>{{ elderInfo?.gender === 1 ? '男' : '女' }}</span>
          </div>
          <div class="flex justify-between">
            <span>出生日期:</span>
            <span>{{
              elderInfo?.birthDate
                ? Array.isArray(elderInfo.birthDate)
                  ? elderInfo.birthDate.join('-')
                  : formatDate(elderInfo.birthDate)
                : ''
            }}</span>
          </div>
          <div class="flex justify-between">
            <span>联系电话:</span>
            <span>{{ maskedPhone }}</span>
          </div>
        </div>
        <el-divider />
        <div>
          <!-- 评估师分析 - 仅在 templateType 为 1 或 2 时显示 -->
          <div v-if="templateType === 1 || templateType === 2" class="mt-4">
            <div class="mb-2">评估师分析</div>
            <div class="rounded-lg text-sm">
              <div class="">
                <el-input
                  type="textarea"
                  v-model="evaluatorAnalysis"
                  :rows="3"
                  placeholder="请输入评估师分析"
                />
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.eval-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 5px;

    .label {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
      font-size: 14px;
    }

    .value {
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}
</style>
