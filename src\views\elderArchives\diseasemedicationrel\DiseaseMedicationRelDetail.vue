<template>
  <Dialog :title="'查看疾病与用药关联详情'" v-model="dialogVisible" width="800px">
    <div class="detail-container" v-loading="detailLoading">
      <!-- 疾病诊断信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>老人与疾病信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">老人姓名：</span>
                <span class="value">{{ detailData.elderName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断日期：</span>
                <span class="value">{{ detailData.diagnosisDate }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断医生：</span>
                <span class="value">{{ detailData.doctorName }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断类型：</span>
                <span class="value">
                  <dict-tag :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="detailData.diagnosisType" />
                </span>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="info-item">
                <span class="label">疾病名称：</span>
                <span class="value">{{ detailData.diseaseName }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 用药信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><Medicine /></el-icon>
          <span>用药信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">记录日期：</span>
                <span class="value">{{ detailData.recordDate }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">记录人员：</span>
                <span class="value">{{ detailData.recorderName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">是否长期用药：</span>
                <span class="value">
                  <el-tag :type="detailData.isLongTerm ? 'success' : 'warning'">
                    {{ detailData.isLongTerm ? '是' : '否' }}
                  </el-tag>
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <div class="info-item">
                <span class="label">药物名称：</span>
                <span class="value">{{ detailData.medicineName }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 关联信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><Link /></el-icon>
          <span>关联信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">关联ID：</span>
                <span class="value">{{ detailData.id }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断ID：</span>
                <span class="value">{{ detailData.diagnosisId }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">用药记录ID：</span>
                <span class="value">{{ detailData.medicationId }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(detailData.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { DiseaseMedicationRelApi, DiseaseMedicationRelVO } from '@/api/elderArchives/diseasemedicationrel'
import { DICT_TYPE } from '@/utils/dict'

/** 疾病与用药关联详情 */
defineOptions({ name: 'DiseaseMedicationRelDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<DiseaseMedicationRelVO>({} as DiseaseMedicationRelVO) // 详情数据

/** 打开弹窗 */
const open = async (row: DiseaseMedicationRelVO) => {
  dialogVisible.value = true
  // 设置数据
  if (row.id) {
    detailLoading.value = true
    try {
      detailData.value = await DiseaseMedicationRelApi.getDiseaseMedicationRel(row.id)
    } finally {
      detailLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;

  .detail-card {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-header {
      padding: 15px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      background-color: #f5f7fa;

      .el-icon {
        margin-right: 10px;
        font-size: 18px;
      }

      span {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .card-content {
      padding: 20px;

      .info-item {
        margin-bottom: 15px;

        .label {
          font-weight: bold;
          color: #606266;
        }

        .value {
          margin-left: 5px;
        }
      }
    }
  }
}
</style>
