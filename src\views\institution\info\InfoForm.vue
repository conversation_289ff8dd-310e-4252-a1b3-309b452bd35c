<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="机构名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入机构名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构简称" prop="shortName">
                <el-input v-model="formData.shortName" placeholder="请输入机构简称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="机构类型" prop="type">
                <el-select v-model="formData.type" placeholder="请选择机构类型" class="w-full">
                  <el-option
                    v-for="dict in getStrDictOptions('organization_type')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构等级" prop="level">
                <el-select v-model="formData.level" placeholder="请选择机构等级" class="w-full">
                  <el-option
                    v-for="dict in getStrDictOptions('organization_level')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="成立时间" prop="establishDate">
                <el-date-picker
                  v-model="formData.establishDate"
                  type="date"
                  value-format="x"
                  placeholder="选择成立时间"
                  class="w-full"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构logo" prop="logo">
                <UploadImg v-model="formData.logo" :file-size="2" directory="institution/logo" />
                <template #tip>
                  <div style="color: #999">支持 jpg、png、gif 格式，大小不超过 2MB</div>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="营业信息" name="business">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="营业执照号码" prop="businessLicense">
                <el-input v-model="formData.businessLicense" placeholder="请输入营业执照号码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="营业执照图片" prop="businessLicenseImg">
                <UploadImg v-model="formData.businessLicenseImg" :file-size="5" directory="institution/license" />
                <template #tip>
                  <div style="color: #999">支持 jpg、png、gif 格式，大小不超过 5MB</div>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="法人代表" prop="legalPerson">
                <el-input v-model="formData.legalPerson" placeholder="请输入法人代表" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人联系方式" prop="legalPersonContact">
                <el-input v-model="formData.legalPersonContact" placeholder="请输入法人联系方式" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="床位数量" prop="bedCount">
                <el-input-number v-model="formData.bedCount" :min="0" placeholder="请输入床位数量" class="w-full" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="占地面积(㎡)" prop="areaSize">
                <el-input-number v-model="formData.areaSize" :min="0" :precision="2" placeholder="请输入占地面积" class="w-full" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="联系方式" name="contact">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急联系电话" prop="emergencyPhone">
                <el-input v-model="formData.emergencyPhone" placeholder="请输入紧急联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="formData.email" placeholder="请输入电子邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="官方网站" prop="website">
                <el-input v-model="formData.website" placeholder="请输入官方网站" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入详细地址" />
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input-number v-model="formData.longitude" :precision="6" placeholder="请输入经度" class="w-full" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input-number v-model="formData.latitude" :precision="6" placeholder="请输入纬度" class="w-full" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="机构介绍" name="introduction">
          <el-form-item label="机构简介" prop="introduction">
            <el-input v-model="formData.introduction" type="textarea" :rows="3" placeholder="请输入机构简介" />
          </el-form-item>
          <el-form-item label="机构特色" prop="features">
            <el-input v-model="formData.features" type="textarea" :rows="3" placeholder="请输入机构特色" />
          </el-form-item>
          <el-form-item label="机构文化" prop="culture">
            <el-input v-model="formData.culture" type="textarea" :rows="3" placeholder="请输入机构文化" />
          </el-form-item>
          <el-form-item label="机构荣誉" prop="honors">
            <el-input v-model="formData.honors" type="textarea" :rows="3" placeholder="请输入机构荣誉" />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { InfoApi, InfoVO } from '@/api/institution/info'
import { getStrDictOptions } from '@/utils/dict'
import UploadImg from '@/components/UploadFile/src/UploadImg.vue'
import './index.css'

/** 机构信息 表单 */
defineOptions({ name: 'InfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const activeTab = ref('basic') // 当前激活的标签页

const formData = ref({
  id: undefined,
  name: undefined,
  shortName: undefined,
  logo: undefined,
  type: undefined,
  level: undefined,
  establishDate: undefined,
  businessLicense: undefined,
  businessLicenseImg: undefined,
  legalPerson: undefined,
  legalPersonContact: undefined,
  bedCount: undefined,
  areaSize: undefined,
  contactPhone: undefined,
  emergencyPhone: undefined,
  email: undefined,
  website: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  introduction: undefined,
  features: undefined,
  culture: undefined,
  honors: undefined
})

// 表单校验规则
const formRules = reactive({
  name: [{ required: true, message: '机构名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '机构类型不能为空', trigger: 'change' }],
  level: [{ required: true, message: '机构等级不能为空', trigger: 'change' }],
  contactPhone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  email: [
    { pattern: /^[\w.-]+@[\w.-]+\.\w+$/, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  website: [
    { pattern: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/, message: '请输入正确的网址', trigger: 'blur' }
  ]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InfoApi.getInfo(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InfoVO
    if (formType.value === 'create') {
      await InfoApi.createInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await InfoApi.updateInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    shortName: undefined,
    logo: undefined,
    type: undefined,
    level: undefined,
    establishDate: undefined,
    businessLicense: undefined,
    businessLicenseImg: undefined,
    legalPerson: undefined,
    legalPersonContact: undefined,
    bedCount: undefined,
    areaSize: undefined,
    contactPhone: undefined,
    emergencyPhone: undefined,
    email: undefined,
    website: undefined,
    address: undefined,
    longitude: undefined,
    latitude: undefined,
    introduction: undefined,
    features: undefined,
    culture: undefined,
    honors: undefined
  }
  formRef.value?.resetFields()
  activeTab.value = 'basic' // 重置为第一个标签页
}
</script>