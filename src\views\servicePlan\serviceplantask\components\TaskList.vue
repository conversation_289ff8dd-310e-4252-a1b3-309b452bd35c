<template>
  <div class="task-list">
    <div class="task-list__header">
      <h3 class="text-lg font-medium">服务任务列表</h3>
      <el-form :inline="true" :model="queryParams" class="task-list__search">
        <el-form-item label="任务名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入任务名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="任务类型">
          <el-input
            v-model="queryParams.taskType"
            placeholder="请输入任务类型"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-200px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="taskList"
      :stripe="true"
      class="task-list__table"
    >
      <el-table-column label="任务名称" align="center" prop="name" />
      <el-table-column label="任务类型" align="center" prop="taskType" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="是否周期任务" align="center" prop="isRecurring">
        <template #default="scope">
          <dict-tag 
            :type="DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING" 
            :value="Number(scope.row.isRecurring)" 
          />
        </template>
      </el-table-column>
      <el-table-column 
        label="开始时间" 
        align="center" 
        prop="startTime" 
      />
      <el-table-column 
        label="结束时间" 
        align="center" 
        prop="endTime" 
      />
      <el-table-column label="优先级" align="center" prop="priority">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY" :value="scope.row.priority" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button 
            link 
            type="primary" 
            @click="handleEdit(scope.row)"
            v-hasPermi="['servicePlan:task:update']"
          >
            编辑
          </el-button>
          <el-button 
            link 
            type="danger" 
            @click="handleDelete(scope.row)"
            v-hasPermi="['servicePlan:task:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  planId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['edit'])

const loading = ref(false)
const taskList = ref<TaskVO[]>([])
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  planId: props.planId || undefined,
  taskType: undefined,
  status: undefined,
  startTime: undefined,
  endTime: undefined,
  name: undefined
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskPage(queryParams)
    taskList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.taskType = undefined
  queryParams.status = undefined
  queryParams.planId = props.planId
  handleQuery()
}

/** 编辑按钮操作 */
const handleEdit = (row: TaskVO) => {
  emit('edit', row.id)
}

/** 删除按钮操作 */
const handleDelete = async (row: TaskVO) => {
  try {
    await ElMessageBox.confirm('是否确认删除该服务任务?', '警告', {
      type: 'warning'
    })
    await TaskApi.deleteTask(row.id)
    ElMessage.success('删除成功')
    await getList()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 初始化
onMounted(() => {
  getList()
})

// 暴露方法给父组件
defineExpose({ getList })
</script>

<style lang="scss" scoped>
.task-list {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;

  &__header {
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 16px 0;
    }
  }

  &__search {
    .el-form-item {
      margin-bottom: 16px;
    }
  }

  &__table {
    margin-bottom: 16px;
  }
}

</style> 