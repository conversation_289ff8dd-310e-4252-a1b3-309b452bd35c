import request from '@/config/axios'

// 用药详情 VO
export interface MedicationDetailVO {
  id: number // 主键ID
  medicationId: number // 用药记录ID
  medicineName: string // 药物名称
  usageMethod: string // 服用方法
  dosage: string // 用药剂量
  frequency: string // 用药频率
  notes: string // 用药注意事项
  createTime: Date // 创建时间
}

// 老人用药记录 VO
export interface MedicationRecordVO {
  id: number // 主键ID
  elderId: number // 老人ID
  elderName?: string // 老人姓名
  recordDate: Date // 记录日期
  recorderName: string // 记录人员
  isLongTerm: boolean // 是否长期用药
  startDate: Date // 开始日期
  endDate: Date // 结束日期
  remark: string // 备注
  createTime?: Date // 创建时间
  medicationDetails?: MedicationDetailVO[] // 用药详情列表
}

// 老人用药记录 API
export const MedicationRecordApi = {
  // 查询老人用药记录分页
  getMedicationRecordPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/medication-record/page`, params })
  },

  // 查询老人用药记录详情
  getMedicationRecord: async (id: number) => {
    return await request.get({ url: `/elderArchives/medication-record/get?id=` + id })
  },

  // 新增老人用药记录
  createMedicationRecord: async (data: MedicationRecordVO) => {
    return await request.post({ url: `/elderArchives/medication-record/create`, data })
  },

  // 修改老人用药记录
  updateMedicationRecord: async (data: MedicationRecordVO) => {
    return await request.put({ url: `/elderArchives/medication-record/update`, data })
  },

  // 删除老人用药记录
  deleteMedicationRecord: async (id: number) => {
    return await request.delete({ url: `/elderArchives/medication-record/delete?id=` + id })
  },

  // 导出老人用药记录 Excel
  exportMedicationRecord: async (params) => {
    return await request.download({ url: `/elderArchives/medication-record/export-excel`, params })
  },
}
