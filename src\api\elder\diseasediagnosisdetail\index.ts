import request from '@/config/axios'

// 疾病诊断详情 VO
export interface DiseaseDiagnosisDetailVO {
  id: number // 主键ID
  diagnosisId: number // 诊断ID
  diseaseCode: string // 疾病编码(ICD-10)
  diseaseName: string // 疾病名称
  description: string // 诊断描述
  isMain: boolean // 是否主要诊断
}

// 疾病诊断详情 API
export const DiseaseDiagnosisDetailApi = {
  // 查询疾病诊断详情分页
  getDiseaseDiagnosisDetailPage: async (params: any) => {
    return await request.get({ url: `/elder/disease-diagnosis-detail/page`, params })
  },

  // 查询疾病诊断详情详情
  getDiseaseDiagnosisDetail: async (id: number) => {
    return await request.get({ url: `/elder/disease-diagnosis-detail/get?id=` + id })
  },

  // 新增疾病诊断详情
  createDiseaseDiagnosisDetail: async (data: DiseaseDiagnosisDetailVO) => {
    return await request.post({ url: `/elder/disease-diagnosis-detail/create`, data })
  },

  // 修改疾病诊断详情
  updateDiseaseDiagnosisDetail: async (data: DiseaseDiagnosisDetailVO) => {
    return await request.put({ url: `/elder/disease-diagnosis-detail/update`, data })
  },

  // 删除疾病诊断详情
  deleteDiseaseDiagnosisDetail: async (id: number) => {
    return await request.delete({ url: `/elder/disease-diagnosis-detail/delete?id=` + id })
  },

  // 导出疾病诊断详情 Excel
  exportDiseaseDiagnosisDetail: async (params) => {
    return await request.download({ url: `/elder/disease-diagnosis-detail/export-excel`, params })
  },
}
