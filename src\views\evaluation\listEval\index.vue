<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ArrowDown } from '@element-plus/icons-vue'

import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile/index'

defineOptions({ name: 'ListEvalIndex' })

import { Template<PERSON>pi } from '@/api/evaluation/template'
import { ResultApi } from '@/api/evaluation/result'
import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'
import formCreate from '@form-create/element-ui'
import { formatDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'

const route = useRoute()
const message = useMessage()
const router = useRouter()

const id = route.query.id

// 步骤条相关
const activeStep = ref(0)
const templateIds = ref<string[]>([])
const currentTemplateId = ref<number>()
const steps = ref<{ title: string; description: string }[]>([])

// 表单相关
const previewForm = ref({})
const previewApi = ref<any>(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: { show: true },
  resetBtn: { show: true }
})

// 上一步相关
const isViewingPreviousStep = ref(false)
const previousStepFormData = ref<any>(null)
const previousStepIndex = ref<number>(-1)

const evaluatorAnalysis = ref<string>('')

// 老人信息
const elderInfo = ref<ArchivesProfileVO | null>(null) // 存储老人信息

// 评估任务清单执行数据
const listExecutionInfo = ref<ListExecutionVO | null>(null)

// 评估基础信息
const evalInfo = reactive({
  elderId: undefined as number | undefined,
  elderName: '',
  templateId: undefined as number | undefined,
  templateName: '',
  evaluatorId: undefined as number | undefined,
  evaluatorName: '',
  evaluationTime: '' as any, // 允许字符串或日期类型
  evaluationReason: ''
})

// 添加模板类型的响应式变量
const templateType = ref<number>(0)

// 添加模板类型选项
const templateTypeOptions = ref<Array<any>>([])

// 存储已完成的模板ID和结果ID数组
const templateIdsArray = ref<string[]>([])
const resultIdsArray = ref<string[]>([])
// 保存每个表单的数据
const formDataMap = ref<Record<string, any>>({})

// 获取表单类型名称的计算属性
const getTemplateTypeName = computed(() => {
  const option = templateTypeOptions.value.find((item: any) => item.value === templateType.value)
  return option ? option.label : '未知类型'
})

// 添加模板详情变量
const templateDetail = ref({
  version: '',
  validityPeriod: 3,
  validityUnit: 'month',
  validityStartTimeType: 'evaluationTime',
  validityStartTime: ''
})

// 添加加载状态
const loading = ref(true)

// 滚动表单到顶部
const scrollToTop = () => {
  nextTick(() => {
    // 处理普通滚动容器
    const formElement = document.querySelector('.evaluation-form')
    if (formElement) {
      formElement.scrollTop = 0
    }

    // 尝试自动点击页面上的el-backtop组件
    const backTopBtn = document.querySelector('.el-backtop')
    if (backTopBtn) {
      ;(backTopBtn as HTMLElement).click()
    } else {
      // 如果没有找到el-backtop组件，则手动滚动页面到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  })
}

// 加载评估任务清单执行数据
const loadListExecutionInfo = async () => {
  if (!id) return

  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 获取评估任务清单执行数据
    const data = await ListExecutionApi.getListExecution(Number(id))
    listExecutionInfo.value = data

    // 填充评估基础信息
    if (data) {
      evalInfo.elderId = data.elderId
      evalInfo.elderName = data.elderName
      evalInfo.evaluatorId = data.evaluatorId
      evalInfo.evaluatorName = data.evaluatorName
      evalInfo.evaluationTime = data.startTime
      evalInfo.evaluationReason = data.evaluationReason

      // 如果有已填充的模板ID，获取模板信息
      if (data.requiredTemplateIds) {
        templateIds.value = data.requiredTemplateIds.split(',')

        // 初始化步骤，先用默认名称
        steps.value = templateIds.value.map((_, index) => ({
          title: `评估表单 ${index + 1}`,
          description: '待完成'
        }))

        // 加载真实的表单名称
        const templatesPromises = templateIds.value.map(async (templateId, index) => {
          try {
            const templateData = await TemplateApi.getTemplate(Number(templateId))
            if (templateData && templateData.name) {
              steps.value[index].title = templateData.name
            }
          } catch (error) {
            ElMessage.error(`加载表单${index + 1}名称失败:`, error)
          }
        })

        // 等待所有表单名称加载完成
        await Promise.all(templatesPromises)

        // 如果有已完成的模板，更新步骤状态
        if (data.completedTemplateIds) {
          const completedIds = data.completedTemplateIds.split(',')
          completedIds.forEach((completedId) => {
            const index = templateIds.value.findIndex((id) => id === completedId)
            if (index !== -1) {
              steps.value[index].description = '已完成'
              // 设置当前步骤为下一个未完成的步骤
              if (index >= activeStep.value) {
                activeStep.value = index + 1
              }
            }
          })
        }

        // 如果有已完成的模板，加载其结果数据
        if (data.completedTemplateIds && data.resultIds) {
          const completedIds = data.completedTemplateIds.split(',')
          const resultIds = data.resultIds.split(',')

          // 逐个加载已完成表单的结果数据
          for (let i = 0; i < completedIds.length; i++) {
            try {
              const resultId = resultIds[i]
              const templateId = completedIds[i]
              const resultData = await ResultApi.getResult(Number(resultId))
              if (resultData && resultData.result) {
                try {
                  const parsedResult = JSON.parse(resultData.result)
                  // console.log('已完成表单结果数据:', templateId, parsedResult)

                  // 保存规则数据到对应表单
                  if (parsedResult.rules) {
                    // 保存规则数据，但不立即应用（会在loadTemplate时应用）
                    formDataMap.value[`${templateId}_rules`] = parsedResult.rules
                  }

                  // 处理表单数据 - 兼容多种数据结构
                  const formValues: any = {}

                  // 首先检查是否有新格式数据结构中的 formData
                  if (parsedResult.formData) {
                    // 新数据结构直接使用 formData
                    Object.assign(formValues, parsedResult.formData)
                  }
                  // 再检查是否有 hierarchicalResults 或 fullResult，这是新的层次化结构
                  else if (parsedResult.fullResult && parsedResult.fullResult.rawFormData) {
                    // 从完整的 JSON 结构中提取 rawFormData
                    Object.assign(formValues, parsedResult.fullResult.rawFormData)
                  }
                  // 最后尝试旧的数据结构
                  else if (
                    parsedResult.assessmentResults &&
                    Array.isArray(parsedResult.assessmentResults)
                  ) {
                    try {
                      parsedResult.assessmentResults.forEach((result: any) => {
                        if (result.items && Array.isArray(result.items)) {
                          result.items.forEach((item: any) => {
                            const rule =
                              item.props && item.props.field ? item.props.field : item.name
                            formValues[rule] = item.value
                          })
                        }
                      })
                    } catch (error) {
                      // console.error('处理旧格式评估结果时出错:', error)
                      ElMessage.error('处理旧格式评估结果时出错')
                    }
                  }

                  // 保存表单数据
                  formDataMap.value[templateId] = formValues
                } catch (parseError) {
                  // console.error(`解析表单${i + 1}结果数据失败:`, parseError)
                  ElMessage.error(`解析表单${i + 1}结果数据失败`)
                }
              }
            } catch (error) {
              // console.error(`加载表单${i + 1}结果数据失败:`, error)
              ElMessage.error(`加载表单${i + 1}结果数据失败`)
            }
          }
        }

        // 加载当前模板
        if (activeStep.value < templateIds.value.length) {
          currentTemplateId.value = Number(templateIds.value[activeStep.value])
          await loadTemplate(currentTemplateId.value)

          // 设置当前步骤为"进行中"状态
          steps.value[activeStep.value].description = '进行中'

          // 获取模板名称
          try {
            const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
            if (templateData) {
              evalInfo.templateName = templateData.name
            }
          } catch (templateError) {
            // console.error('获取模板信息失败:', templateError)
            ElMessage.error('获取模板信息失败')
          }
        } else {
          // 所有模板都已完成
          message.success('所有评估表单已完成')
        }
      }

      // 如果有评估师分析，填充评估师分析
      if (data.evaluatorAnalysis) {
        evaluatorAnalysis.value = data.evaluatorAnalysis
      }

      // 根据elderId获取老人详细信息
      await loadElderInfo(data.elderId)

      // 初始化已完成的模板ID和结果ID数组
      if (data.completedTemplateIds) {
        templateIdsArray.value = data.completedTemplateIds.split(',')
      }
      if (data.resultIds) {
        resultIdsArray.value = data.resultIds.split(',')
      }
    }
  } catch (error) {
    // console.error('加载评估任务清单执行数据失败:', error)
    ElMessage.error('加载评估任务清单执行数据失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 加载模板数据
const loadTemplate = async (templateId: number) => {
  if (!templateId) return

  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 获取模板详情
    const data = await TemplateApi.getTemplate(templateId)
    templateDetail.value = data

    // 解析formSchema提取有效期设置
    if (data.formSchema) {
      try {
        const schema = JSON.parse(data.formSchema)
        // console.log(schema)
        if (schema.option && schema.option.form) {
          templateDetail.value.validityPeriod = schema.option.form.validityPeriod || 3
          templateDetail.value.validityUnit = schema.option.form.validityUnit || 'month'
          templateDetail.value.validityStartTimeType =
            schema.option.form.validityStartTimeType || 'evaluationTime'
          templateDetail.value.validityStartTime = schema.option.form.validityStartTime || ''
        }
      } catch (e) {
        // console.error('解析模板数据失败:', e)
        ElMessage.error('解析模板数据失败')
      }
    }

    // 其他模板加载逻辑
    if (data.formSchema) {
      const schema = JSON.parse(data.formSchema)

      // 检查是否有存储的规则数据
      const storedRules = formDataMap.value[`${templateId}_rules`]
      if (storedRules && isViewingPreviousStep.value) {
        // console.log('使用已保存的规则数据:', storedRules)
        templateRule.value = storedRules
      } else {
        templateRule.value = schema.rule || []
      }

      // 保存完整的原始模板选项
      templateOption.value = {
        ...templateOption.value,
        ...schema.option,
        formName: schema.option?.formName || evalInfo.templateName,
        form: schema.option?.form || {}
      }

      // 如果是查看上一步，则修改提交按钮文本为"更新此步骤"
      if (isViewingPreviousStep.value) {
        templateOption.value.submitBtn = {
          show: true
        }
        // 单独设置按钮文本，避免类型错误
        templateOption.value.submitBtn['innerText'] = '更新此步骤'
      } else {
        templateOption.value.submitBtn = {
          show: true
        }
        // 单独设置按钮文本，避免类型错误
        templateOption.value.submitBtn['innerText'] = '提交'
      }

      // 获取模板类型
      templateType.value = schema.option?.form?.templateType || 0

      // 滚动表单到顶部
      scrollToTop()
    }
  } catch (error) {
    // console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 加载老人信息
const loadElderInfo = async (elderId: number) => {
  if (!elderId) return

  try {
    loading.value = true // 开始加载，设置加载状态为true
    const elderData = await ArchivesProfileApi.getArchivesProfile(elderId)
    elderInfo.value = elderData
    // console.log('老人信息:', elderInfo.value)
  } catch (error) {
    // console.error('加载老人信息失败:', error)
    ElMessage.error('加载老人信息失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 加载评估基础信息
const loadEvalInfo = async () => {
  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 首先尝试从URL参数加载数据
    const query = route.query

    // 如果有id，优先根据id加载数据
    if (id) {
      await loadListExecutionInfo()
      return
    }

    // 如果没有id但有其他参数，使用URL参数
    evalInfo.elderId = query.elderId ? parseInt(query.elderId as string) : undefined
    evalInfo.elderName = (query.elderName as string) || ''
    evalInfo.templateId = query.templateId ? parseInt(query.templateId as string) : undefined
    evalInfo.templateName = (query.templateName as string) || ''
    evalInfo.evaluatorId = query.evaluatorId ? parseInt(query.evaluatorId as string) : undefined
    evalInfo.evaluatorName = (query.evaluatorName as string) || ''
    evalInfo.evaluationTime = query.evaluationTime || ''
    evalInfo.evaluationReason = (query.evaluationReason as string) || ''

    // 根据 elderId 获取老人信息
    if (evalInfo.elderId) {
      await loadElderInfo(evalInfo.elderId)
    }
  } catch (error) {
    // console.error('加载评估信息失败:', error)
    ElMessage.error('加载评估信息失败')
  } finally {
    // console.log('loadEvalInfo - 完成加载')
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 处理表单提交
const handleSubmit = async (formData: any) => {
  // console.log('提交的表单数据:', formData)

  // 检查formData是否只包含_vts和isTrusted而没有实际的表单数据
  const hasOnlyMetaFields = Object.keys(formData).every((key) =>
    ['_vts', 'isTrusted'].includes(key)
  )

  // 如果只有元数据字段，尝试从form-create API获取完整的表单数据
  if (hasOnlyMetaFields && previewApi.value) {
    try {
      const completeFormData = previewApi.value.formData()
      // console.log('从API获取的完整表单数据:', completeFormData)

      // 如果获取到有效的表单数据，则使用它
      if (completeFormData && Object.keys(completeFormData).length > Object.keys(formData).length) {
        formData = completeFormData
      }
    } catch (error) {
      // console.error('获取完整表单数据失败:', error)
      ElMessage.error('获取完整表单数据失败')
    }
  }

  // 复制规则以避免修改原始数据
  const copyRules = formCreate.copyRules(templateRule.value)
  const options = templateOption.value

  // 常规提交处理...
  try {
    loading.value = true // 开始加载，设置加载状态为true

    let aiInput = `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 构建字段和标题的映射关系
    const fieldTitleMap = {}
    const fieldOptionsMap = {}
    const fieldParentMap = {}
    const fieldMidParentMap = {}
    const fieldTopParentMap = {}

    // 递归函数，用于从规则中提取字段信息
    const extractFieldInfo = (
      rules,
      parentTitle = '',
      midParentTitle = '',
      topParentTitle = ''
    ) => {
      if (!rules || !Array.isArray(rules)) return

      rules.forEach((rule) => {
        // 跳过elAlert类型
        if (rule._fc_drag_tag === 'elAlert') return

        // 获取当前项的标题
        let currentTitle = ''
        if (rule.props?.header) {
          currentTitle = rule.props.header
        } else if (rule.title && rule.title.trim() !== '') {
          currentTitle = rule.title
        } else if (rule.name?.startsWith('ref_') && rule.info && rule.info.trim() !== '') {
          currentTitle = rule.info
        } else if (rule.name && !rule.name.startsWith('ref_')) {
          currentTitle = rule.name
        }

        // 记录规则的基本信息
        // if (rule.field) {
        //   console.log(
        //     `提取字段信息: 字段=${rule.field}, 标题=${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // } else if (currentTitle) {
        //   console.log(
        //     `处理标题: ${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // }

        // 如果有字段，记录它的信息
        if (rule.field) {
          fieldTitleMap[rule.field] = currentTitle || ''
          fieldParentMap[rule.field] = parentTitle || ''
          fieldMidParentMap[rule.field] = midParentTitle || ''
          fieldTopParentMap[rule.field] = topParentTitle || ''

          if (rule.options && rule.options.length > 0) {
            fieldOptionsMap[rule.field] = rule.options
            // console.log(`字段${rule.field}的选项:`, rule.options)
          }
        }

        // 如果有子项，递归处理
        if (rule.children && rule.children.length > 0) {
          // 确定标题的层级关系
          let newTopParent = topParentTitle
          let newMidParent = midParentTitle

          // 如果是顶层卡片
          if (!parentTitle && rule._fc_drag_tag === 'elCard') {
            newTopParent = currentTitle
          }
          // 如果已有顶层父级，但没有中间父级
          else if (topParentTitle && !midParentTitle && rule._fc_drag_tag === 'elCard') {
            newMidParent = currentTitle
          }

          extractFieldInfo(rule.children, currentTitle || parentTitle, newMidParent, newTopParent)
        }
      })
    }

    // 提取字段信息
    // console.log('开始提取字段信息...')
    extractFieldInfo(copyRules)
    // console.log('字段标题映射:', fieldTitleMap)
    // console.log('字段父标题映射:', fieldParentMap)
    // console.log('字段中间父标题映射:', fieldMidParentMap)
    // console.log('字段顶层标题映射:', fieldTopParentMap)
    // console.log('字段选项映射:', fieldOptionsMap)

    // 处理formData，构建aiInput
    // console.log('开始构建aiInput...')

    // 将评估结果组织成层级结构，使用数组保持顺序
    const hierarchicalResults = {}
    // 记录处理顺序
    const processOrder = {
      topParents: [],
      midParents: {},
      parentTitles: {},
      fields: {}
    }

    // 对字段进行分组和层级化处理
    for (const field in formData) {
      if (fieldTopParentMap[field]) {
        const topParent = fieldTopParentMap[field]
        const midParent = fieldMidParentMap[field]
        const parentTitle = fieldParentMap[field]
        const fieldTitle = fieldTitleMap[field]

        // 记录处理顺序
        // 记录顶层标题顺序
        if (!processOrder.topParents.includes(topParent)) {
          processOrder.topParents.push(topParent)
        }

        // 记录中间标题顺序
        if (midParent && midParent !== topParent) {
          if (!processOrder.midParents[topParent]) {
            processOrder.midParents[topParent] = []
          }
          if (!processOrder.midParents[topParent].includes(midParent)) {
            processOrder.midParents[topParent].push(midParent)
          }

          // 记录直接父标题顺序（在中间标题下）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.parentTitles[midKey]) {
              processOrder.parentTitles[midKey] = []
            }
            if (!processOrder.parentTitles[midKey].includes(parentTitle)) {
              processOrder.parentTitles[midKey].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${midParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在中间标题下）
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.fields[midKey]) {
              processOrder.fields[midKey] = []
            }
            processOrder.fields[midKey].push(field)
          }
        } else {
          // 记录直接父标题顺序（在顶层标题下）
          if (parentTitle && parentTitle !== topParent) {
            if (!processOrder.parentTitles[topParent]) {
              processOrder.parentTitles[topParent] = []
            }
            if (!processOrder.parentTitles[topParent].includes(parentTitle)) {
              processOrder.parentTitles[topParent].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在顶层标题下）
            if (!processOrder.fields[topParent]) {
              processOrder.fields[topParent] = []
            }
            processOrder.fields[topParent].push(field)
          }
        }

        // 处理值
        let resultValue = formData[field]
        let resultLabel = ''
        if (fieldOptionsMap[field]) {
          if (Array.isArray(resultValue)) {
            const labels = resultValue.map((val) => {
              const option = fieldOptionsMap[field].find((opt) => String(opt.value) === String(val))
              return option ? option.label : '未找到标签'
            })
            resultLabel = labels.join('、')
          } else {
            const option = fieldOptionsMap[field].find(
              (opt) => String(opt.value) === String(resultValue)
            )
            resultLabel = option ? option.label : '未找到标签'
          }
        } else {
          resultLabel = String(resultValue)
        }

        // 创建层级结构
        if (!hierarchicalResults[topParent]) {
          hierarchicalResults[topParent] = {
            title: topParent,
            children: [],
            childrenMap: {} // 用于快速查找
          }
        }

        // 添加中间父级（如果存在）
        if (midParent && midParent !== topParent) {
          // 检查中间父级是否已存在
          if (!hierarchicalResults[topParent].childrenMap[midParent]) {
            const midParentNode = {
              title: midParent,
              parent: topParent,
              children: [],
              childrenMap: {} // 用于快速查找
            }
            hierarchicalResults[topParent].children.push(midParentNode)
            hierarchicalResults[topParent].childrenMap[midParent] = midParentNode
          }

          // 添加直接父级（如果存在且与中间父级不同）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            // 检查直接父级是否已存在
            if (!midParentNode.childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: midParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              midParentNode.children.push(parentNode)
              midParentNode.childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = midParentNode.childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与中间父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: midParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            midParentNode.children.push(fieldNode)
            midParentNode.childrenMap[fieldKey] = fieldNode
          }
        }
        // 如果没有中间父级
        else {
          // 添加直接父级（如果存在且与顶层父级不同）
          if (parentTitle && parentTitle !== topParent) {
            // 检查直接父级是否已存在
            if (!hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: topParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              hierarchicalResults[topParent].children.push(parentNode)
              hierarchicalResults[topParent].childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与顶层父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: topParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            hierarchicalResults[topParent].children.push(fieldNode)
            hierarchicalResults[topParent].childrenMap[fieldKey] = fieldNode
          }
        }

        // 构建文本输出
        aiInput += `${topParent}\n`
        if (midParent && midParent !== topParent) {
          aiInput += `${midParent}\n`
        }
        if (parentTitle && parentTitle !== topParent && parentTitle !== midParent) {
          aiInput += `${parentTitle}\n`
        }
        if (
          fieldTitle &&
          fieldTitle !== parentTitle &&
          fieldTitle !== midParent &&
          fieldTitle !== topParent
        ) {
          aiInput += `${fieldTitle}\n`
        }
        aiInput += `${resultLabel}\n\n`
      }
    }

    // 最终处理，根据处理顺序重新组织结果，并删除临时的childrenMap
    const orderedResults = {}
    processOrder.topParents.forEach((topParent) => {
      if (hierarchicalResults[topParent]) {
        // 创建有序的顶级结果
        const topResult = {
          title: hierarchicalResults[topParent].title,
          children: []
        }

        // 处理中间父级（如果有）
        if (processOrder.midParents[topParent]) {
          processOrder.midParents[topParent].forEach((midParent) => {
            if (hierarchicalResults[topParent].childrenMap[midParent]) {
              const midNode = hierarchicalResults[topParent].childrenMap[midParent]
              const orderedMidNode = {
                title: midNode.title,
                parent: midNode.parent,
                children: []
              }

              // 处理直接父级（如果有）
              const midKey = `${topParent}:${midParent}`
              if (processOrder.parentTitles[midKey]) {
                processOrder.parentTitles[midKey].forEach((parentTitle) => {
                  if (midNode.childrenMap[parentTitle]) {
                    const parentNode = midNode.childrenMap[parentTitle]
                    const orderedParentNode = {
                      title: parentNode.title,
                      parent: parentNode.parent,
                      children: []
                    }

                    // 处理字段
                    const parentKey = `${topParent}:${midParent}:${parentTitle}`
                    if (processOrder.fields[parentKey]) {
                      processOrder.fields[parentKey].forEach((field) => {
                        const fieldKey = fieldTitleMap[field] || field
                        if (parentNode.childrenMap[fieldKey]) {
                          orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                        }
                      })
                    }

                    orderedMidNode.children.push(orderedParentNode)
                  }
                })
              }

              // 处理直接在中间父级下的字段
              if (processOrder.fields[midKey]) {
                processOrder.fields[midKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (midNode.childrenMap[fieldKey] && !midNode.childrenMap[fieldKey].children) {
                    orderedMidNode.children.push(midNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedMidNode)
            }
          })
        }

        // 处理直接父级（如果没有中间父级）
        if (processOrder.parentTitles[topParent]) {
          processOrder.parentTitles[topParent].forEach((parentTitle) => {
            if (hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
              const orderedParentNode = {
                title: parentNode.title,
                parent: parentNode.parent,
                children: []
              }

              // 处理字段
              const parentKey = `${topParent}:${parentTitle}`
              if (processOrder.fields[parentKey]) {
                processOrder.fields[parentKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (parentNode.childrenMap[fieldKey]) {
                    orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedParentNode)
            }
          })
        }

        // 处理直接在顶级下的字段
        if (processOrder.fields[topParent]) {
          processOrder.fields[topParent].forEach((field) => {
            const fieldKey = fieldTitleMap[field] || field
            if (
              hierarchicalResults[topParent].childrenMap[fieldKey] &&
              !hierarchicalResults[topParent].childrenMap[fieldKey].children
            ) {
              topResult.children.push(hierarchicalResults[topParent].childrenMap[fieldKey])
            }
          })
        }

        orderedResults[topParent] = topResult
      }
    })

    // console.log('原始层级化结果:', hierarchicalResults)
    // console.log('处理顺序:', processOrder)
    // console.log('有序层级化结果:', orderedResults)
    // console.log('最终AI输入:\n', aiInput)

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evalInfo.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value ? new Date().getFullYear() - elderInfo.value.birthDate[0] : ''
        },
        templateInfo: {
          id: evalInfo.templateId || 0,
          name: evalInfo.templateName,
          type: templateType.value,
          validityPeriod: templateDetail.value?.validityPeriod || 3,
          validityUnit: templateDetail.value?.validityUnit || 'month',
          validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
          validityStartTime: templateDetail.value?.validityStartTime || ''
        },
        evaluationInfo: {
          evaluatorId: evalInfo.evaluatorId || 0,
          evaluatorName: evalInfo.evaluatorName,
          evaluationReason: evalInfo.evaluationReason,
          evaluationTime: new Date().getTime()
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInput,
      hierarchicalResults: orderedResults,
      rawFormData: formData
    }

    // 将完整结果转换为JSON字符串
    const fullResultJSONString = JSON.stringify(fullResultJSON, null, 2)
    // console.log('完整JSON结果:', fullResultJSONString)

    // if (templateType.value !== 0) {
    //   if (!evaluatorAnalysis.value) {
    //     message.error('评估师分析不能为空')
    //     return false
    //   }
    // }

    // 构建提交数据
    const resultData = {
      elderId: evalInfo.elderId || 0,
      elderName: elderInfo.value?.name || '',
      templateId: currentTemplateId.value || 0,
      templateName: evalInfo.templateName,
      evaluationReason: evalInfo.evaluationReason,
      evaluatorId: evalInfo.evaluatorId || 0,
      evaluatorName: evalInfo.evaluatorName,
      evaluationTime: new Date().getTime(),
      type: 1,
      aiInputs: aiInput,
      aiAnalysis: '',
      evaluatorAnalysis: evaluatorAnalysis.value,
      result: JSON.stringify({
        options: options,
        rules: copyRules,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      })
    }

    let resultId

    // 判断是更新还是创建新结果
    if (isViewingPreviousStep.value && previousStepIndex.value >= 0) {
      // 更新操作：获取当前查看的模板和对应的结果ID
      const prevTemplateId = templateIds.value[previousStepIndex.value]
      const prevResultId = resultIdsArray.value[previousStepIndex.value]

      if (prevResultId) {
        // console.log('更新结果:', prevResultId)
        // 使用updateResult API
        await ResultApi.updateResult({
          ...resultData,
          id: Number(prevResultId)
        })

        // 保存更新后的表单数据
        formDataMap.value[prevTemplateId] = formData

        message.success('更新成功')

        // 更新操作不需要改变步骤状态或处理下一步
        // 跳过后续的列表执行更新部分
        return true // 返回成功状态
      } else {
        // console.error('未找到结果ID进行更新')
        message.error('更新失败：未找到相应的结果记录')
        return false // 返回失败状态
      }
    } else {
      // 创建新结果
      // console.log('创建新结果')
      resultId = await ResultApi.createResult(resultData)
      // console.log('result', resultId)
    }

    // 更新评估任务清单执行状态 (仅在创建新结果时执行)
    if (listExecutionInfo.value) {
      // 添加新的ID到数组中
      templateIdsArray.value.push(String(currentTemplateId.value))
      resultIdsArray.value.push(String(resultId))

      // 转换回逗号分隔的字符串
      const newCompletedTemplateIds = templateIdsArray.value.join(',')
      const newResultIds = resultIdsArray.value.join(',')

      // 检查是否完成了所有步骤
      const isAllCompleted = activeStep.value === templateIds.value.length - 1

      // 更新评估任务清单执行
      await ListExecutionApi.updateListExecution({
        ...listExecutionInfo.value,
        completedTemplateIds: newCompletedTemplateIds,
        resultIds: newResultIds,
        // 如果完成所有评估,更新完成时间和状态
        ...(isAllCompleted
          ? {
              endTime: new Date().getTime(),
              status: 1 // 假设1表示已完成状态
            }
          : {})
      })

      // 更新当前步骤状态
      steps.value[activeStep.value].description = '已完成'

      // 如果还有下一步，加载下一个模板
      if (activeStep.value < templateIds.value.length - 1) {
        activeStep.value++
        currentTemplateId.value = Number(templateIds.value[activeStep.value])
        await loadTemplate(currentTemplateId.value)

        // 将新的当前步骤设置为"进行中"
        steps.value[activeStep.value].description = '进行中'

        // 获取模板名称
        try {
          const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
          if (templateData) {
            evalInfo.templateName = templateData.name

            // 滚动表单到顶部
            scrollToTop()
          }
        } catch (templateError) {
          // console.error('获取模板信息失败:', templateError)
          ElMessage.error('获取模板信息失败')
        }
      } else {
        // 所有步骤完成，返回列表页
        message.success('所有评估表单已完成')
        router.push('/evaluation/result')
      }
    }

    message.success('保存成功')
    return true
  } catch (error) {
    // console.error('保存评估结果失败:', error)
    ElMessage.error('保存评估结果失败')
    return false
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 获取有效期单位文本
const getValidityUnitText = (unit) => {
  switch (unit) {
    case 'day':
      return '天'
    case 'week':
      return '周'
    case 'month':
    default:
      return '个月'
  }
}

// 查看上一步表单结果
const viewPreviousStep = async (index: number) => {
  if (index < 0 || index >= activeStep.value) {
    message.error('无效的步骤索引')
    return
  }

  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 记录当前查看的步骤索引
    previousStepIndex.value = index
    isViewingPreviousStep.value = true

    // 获取上一步的模板ID和结果ID
    const prevTemplateId = Number(templateIds.value[index])
    const prevResultId = resultIdsArray.value[index]

    // 加载上一步模板
    await loadTemplate(prevTemplateId)

    // 隐藏form-create默认提交按钮
    if (templateOption.value.submitBtn) {
      templateOption.value.submitBtn.show = false
    } else {
      templateOption.value.submitBtn = { show: false }
    }

    // 获取上一步的表单数据并设置
    let prevFormData = formDataMap.value[templateIds.value[index]]

    // 如果缓存中没有表单数据，则从API获取
    if (!prevFormData && prevResultId) {
      try {
        // console.log('从API获取表单结果:', prevResultId)
        const resultData = await ResultApi.getResult(Number(prevResultId))

        if (resultData && resultData.result) {
          // 解析结果数据
          const parsedResult = JSON.parse(resultData.result)
          // console.log('API获取的表单结果数据:', parsedResult)

          // 处理表单数据 - 兼容多种数据结构
          if (parsedResult.formData) {
            // 新数据结构直接使用 formData
            prevFormData = parsedResult.formData
          }
          // 再检查是否有 hierarchicalResults 或 fullResult，这是新的层次化结构
          else if (parsedResult.fullResult && parsedResult.fullResult.rawFormData) {
            // 从完整的 JSON 结构中提取 rawFormData
            prevFormData = parsedResult.fullResult.rawFormData
          }
          // 最后尝试旧的数据结构
          else if (
            parsedResult.assessmentResults &&
            Array.isArray(parsedResult.assessmentResults)
          ) {
            prevFormData = {}
            try {
              parsedResult.assessmentResults.forEach((result: any) => {
                if (result.items && Array.isArray(result.items)) {
                  result.items.forEach((item: any) => {
                    const rule = item.props && item.props.field ? item.props.field : item.name
                    prevFormData[rule] = item.value
                  })
                }
              })
            } catch (error) {
              // console.error('处理旧格式评估结果时出错:', error)
              ElMessage.error('处理旧格式评估结果时出错')
            }
          }

          // 如果有规则数据，保存到规则缓存中
          if (parsedResult.rules) {
            formDataMap.value[`${prevTemplateId}_rules`] = parsedResult.rules
          }

          // 保存到缓存中
          if (prevFormData) {
            formDataMap.value[templateIds.value[index]] = prevFormData
          }
        }
      } catch (apiError) {
        // console.error('从API获取表单结果失败:', apiError)
        ElMessage.error('无法获取表单历史数据')
      }
    }

    if (prevFormData) {
      // 保存当前表单数据，以便返回时恢复
      previousStepFormData.value = JSON.parse(JSON.stringify(prevFormData))

      // console.log('设置上一步表单数据:', prevFormData)

      // 先设置表单值引用
      previewForm.value = prevFormData

      // 等待组件渲染完成后设置表单数据
      nextTick(() => {
        if (previewApi.value) {
          try {
            // 先重置表单，避免数据混淆
            previewApi.value.resetFields()

            // 然后使用setValue方法设置表单值
            previewApi.value.setValue(prevFormData)

            // 额外确认表单数据是否被正确设置
            const currentFormData = previewApi.value.formData()
            // console.log('设置后的表单数据确认:', currentFormData)

            // 如果需要，强制更新表单
            nextTick(() => {
              if (previewApi.value) {
                previewApi.value.refresh()
              }
            })
          } catch (error) {
            // console.error('设置表单数据失败:', error)
            ElMessage.error('设置表单数据失败')
          }
        }
      })
    } else {
      message.warning('未找到表单历史数据')
    }

    // 更新活动步骤，但不影响已完成状态
    currentTemplateId.value = prevTemplateId

    // 更新评估表单名称
    evalInfo.templateName = steps.value[index]?.title || `评估表单 ${index + 1}`

    // 获取当前模板的完整信息以更新类型和有效期
    try {
      const templateData = await TemplateApi.getTemplate(prevTemplateId)
      if (templateData) {
        evalInfo.templateName = templateData.name

        // 解析formSchema提取模板类型和有效期设置
        if (templateData.formSchema) {
          try {
            const schema = JSON.parse(templateData.formSchema)
            if (schema.option && schema.option.form) {
              templateType.value = schema.option.form.templateType || 0
            }
          } catch (e) {
            // console.error('解析模板类型数据失败:', e)
            ElMessage.error('解析模板类型数据失败')
          }
        }
      }
    } catch (templateError) {
      // console.error('获取模板详情信息失败:', templateError)
      ElMessage.error('获取模板详情信息失败')
    }

    message.success(`正在查看${steps.value[index]?.title || `第${index + 1}步表单`}`)
  } catch (error) {
    // console.error('查看上一步表单失败:', error)
    ElMessage.error('查看上一步表单失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 返回当前步骤
const returnToCurrentStep = async () => {
  if (!isViewingPreviousStep.value) return

  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 重置查看上一步的状态
    isViewingPreviousStep.value = false
    previousStepIndex.value = -1

    // 显示form-create默认提交按钮
    if (templateOption.value.submitBtn) {
      templateOption.value.submitBtn.show = true
      templateOption.value.submitBtn['innerText'] = '提交'
    } else {
      templateOption.value.submitBtn = { show: true }
      templateOption.value.submitBtn['innerText'] = '提交'
    }

    // 加载当前步骤的模板
    currentTemplateId.value = Number(templateIds.value[activeStep.value])
    await loadTemplate(currentTemplateId.value)

    // 清空表单数据，因为是当前正在进行的步骤
    if (previewApi.value) {
      previewApi.value.resetFields()
    }

    // 获取当前模板的完整信息以更新评估表单名称
    try {
      const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
      if (templateData) {
        evalInfo.templateName = templateData.name

        // 解析formSchema提取模板类型和有效期设置
        if (templateData.formSchema) {
          try {
            const schema = JSON.parse(templateData.formSchema)
            if (schema.option && schema.option.form) {
              templateType.value = schema.option.form.templateType || 0
            }
          } catch (e) {
            // console.error('解析模板类型数据失败:', e)
            ElMessage.error('解析模板类型数据失败')
          }
        }
      }
    } catch (templateError) {
      // console.error('获取模板详情信息失败:', templateError)
      ElMessage.error('获取模板详情信息失败')
    }

    message.success('已返回当前步骤')
  } catch (error) {
    // console.error('返回当前步骤失败:', error)
    ElMessage.error('返回当前步骤失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 手动更新步骤 - 按钮事件处理函数
const manualUpdateStep = async () => {
  try {
    // 显示加载状态
    loading.value = true

    if (!previewApi.value) {
      message.error('表单API未初始化')
      return
    }

    // 直接从form-create API获取完整表单数据
    const formData = previewApi.value.formData()
    // console.log('手动更新 - 获取到的表单数据:', formData)

    // 验证表单数据是否为空或只包含元数据
    const fieldKeys = Object.keys(formData).filter((key) => !['_vts', 'isTrusted'].includes(key))
    if (fieldKeys.length === 0) {
      message.error('无法获取有效的表单数据，请检查表单')
      return
    }

    // 调用提交处理函数
    const success = await handleSubmit(formData)

    // 如果更新成功，自动返回当前步骤
    if (success) {
      // 等待一段时间再返回，让用户看到成功消息
      setTimeout(() => {
        returnToCurrentStep()
        scrollToTop()
      }, 1000)
    }
  } catch (error) {
    // console.error('手动更新表单数据失败:', error)
    ElMessage.error('手动更新表单数据失败')
  } finally {
    loading.value = false
  }
}

// 添加计算剩余有效期的函数
const getRemainingValidity = () => {
  if (
    !templateDetail.value ||
    templateDetail.value.validityStartTimeType !== 'fixedDate' ||
    !templateDetail.value.validityStartTime
  ) {
    return null
  }

  try {
    // 获取固定起始日期
    const startDate = new Date(templateDetail.value.validityStartTime)
    // 获取有效期和单位
    const validityPeriod = templateDetail.value.validityPeriod || 3
    const validityUnit = templateDetail.value.validityUnit || 'month'

    // 计算到期日期
    let expiryDate = new Date(startDate)
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const today = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    return daysLeft > 0 ? daysLeft : 0
  } catch (error) {
    // console.error('计算剩余有效期失败:', error)
    ElMessage.error('计算剩余有效期失败')
    return null
  }
}

// 初始化
onMounted(() => {
  loading.value = true // 开始加载，设置加载状态为true
  templateTypeOptions.value = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  loadEvalInfo()
})

// 工具函数：脱敏处理
const maskString = (str: string, start: number, end: number) => {
  if (!str) return ''
  const maskLength = end - start
  const maskStr = '*'.repeat(maskLength)
  return str.substring(0, start) + maskStr + str.substring(end)
}

// 计算属性：脱敏后的身份证号
const maskedIdNumber = computed(() => {
  if (!elderInfo.value?.idNumber) return ''
  return maskString(elderInfo.value.idNumber, 6, 14)
})

// 计算属性：脱敏后的联系电话
const maskedPhone = computed(() => {
  if (!elderInfo.value?.contactPhone) return ''
  return maskString(elderInfo.value.contactPhone, 3, 7)
})
</script>

<template>
  <div class="flex flex-col h-full" v-loading="loading" element-loading-text="数据加载中...">
    <div class="flex flex-1">
      <div class="flex-1 mr-4">
        <!-- 添加步骤条 -->
        <el-card class="mb-1">
          <div class="flex justify-between items-center mb-2">
            <el-button @click="router.back()" :icon="ArrowLeft">返回</el-button>
            <!-- 查看上一步相关按钮 -->
            <div v-if="isViewingPreviousStep" class="flex items-center">
              <el-tag type="warning" class="mr-2"
                >正在查看{{
                  steps[previousStepIndex]?.title || `第${previousStepIndex + 1}步表单`
                }}，可以修改后提交更新</el-tag
              >
              <el-button type="primary" @click="returnToCurrentStep">返回当前步骤</el-button>
            </div>
          </div>
          <el-steps :active="activeStep" finish-status="success" class="mb-4">
            <el-step
              v-for="(step, index) in steps"
              :key="index"
              :title="step.title"
              :description="step.description"
            />
          </el-steps>
        </el-card>

        <el-card>
          <div class="flex justify-between items-center">
            <h2>{{ evalInfo.templateName }}</h2>
            <!-- 显示已完成步骤的查看按钮 -->
            <div v-if="!isViewingPreviousStep && activeStep > 0" class="flex">
              <el-dropdown trigger="click">
                <el-button type="primary">
                  查看上一步 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="(step, index) in steps.slice(0, activeStep)"
                      :key="index"
                      @click="viewPreviousStep(index)"
                    >
                      {{ step.title }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div class="text-gray-500 text-sm mt-2 mb-2">
            <span class="mr-4">表单类型: {{ getTemplateTypeName }}</span>
            <span class="mr-4">版本号: {{ templateDetail.version || '1.0.0' }}</span>
            <span>
              <template
                v-if="
                  templateDetail.validityStartTimeType === 'fixedDate' &&
                  templateDetail.validityStartTime
                "
              >
                剩余有效期: {{ getRemainingValidity() }} 天 (总有效期
                {{ templateDetail.validityPeriod || 3 }}
                {{ getValidityUnitText(templateDetail.validityUnit || 'month') }}，从
                {{ templateDetail.validityStartTime }}
                起)
              </template>
              <template v-else>
                有效期: {{ templateDetail.validityPeriod || 3 }}
                {{ getValidityUnitText(templateDetail.validityUnit || 'month') }} (从评估时间起)
              </template>
            </span>
          </div>
          <el-divider />
          <div class="evaluation-form" style="max-height: 50vh; overflow-y: auto">
            <form-create
              v-model:modelValue="previewForm"
              v-model:api="previewApi"
              :rule="templateRule"
              :option="templateOption"
              @submit="handleSubmit"
            />
          </div>

          <!-- 修改表单按钮文本 -->
          <div v-if="isViewingPreviousStep" class="mt-4 flex justify-end">
            <el-button type="primary" @click="manualUpdateStep">更新此步骤</el-button>
            <el-button @click="returnToCurrentStep">取消修改</el-button>
          </div>
        </el-card>
      </div>

      <!-- 老人信息 -->
      <el-card class="bg-white p-3 rounded-lg w-110">
        <!-- 评估信息 -->

        <div class="eval-info">
          <div class="info-item">
            <span class="label">评估师：</span>
            <span class="value">{{ evalInfo.evaluatorName }}</span>
          </div>
          <div class="info-item">
            <span class="label">评估原因：</span>
            <span class="value">{{ evalInfo.evaluationReason }}</span>
          </div>
          <!-- <div class="info-item">
            <span class="label">评估时间：</span>
            <span class="value">{{ timestampToDate(evalInfo.evaluationTime) }}</span>
          </div> -->
        </div>
        <div class="flex flex-col items-center">
          <!-- <div class="w-24 h-24 rounded-full overflow-hidden mb-4">
            <img
              src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
              class="w-full h-full object-cover"
              alt="老人头像"
            />
          </div>
          <h3 class="text-lg font-medium">{{ elderName }}</h3> -->
        </div>
        <div class="mt-1 space-y-4">
          <div class="flex justify-between">
            <span>老人姓名:</span>
            <span>{{ elderInfo?.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>年龄:</span>
            <span>{{ elderInfo ? new Date().getFullYear() - elderInfo.birthDate[0] : '' }}岁</span>
          </div>
          <div class="flex justify-between">
            <span>身份证号:</span>
            <span>{{ maskedIdNumber }}</span>
          </div>
          <div class="flex justify-between">
            <span>性别:</span>
            <span>{{ elderInfo?.gender === 1 ? '男' : '女' }}</span>
          </div>
          <div class="flex justify-between">
            <span>出生日期:</span>
            <span>{{
              elderInfo?.birthDate
                ? Array.isArray(elderInfo.birthDate)
                  ? elderInfo.birthDate.join('-')
                  : formatDate(elderInfo.birthDate)
                : ''
            }}</span>
          </div>
          <div class="flex justify-between">
            <span>联系电话:</span>
            <span>{{ maskedPhone }}</span>
          </div>
        </div>
        <el-divider />
        <div>
          <!-- 评估结果展示 - 仅在 templateType 为 1 或 2 时显示 -->
          <!-- <div v-if="templateType === 1 || templateType === 2" class="mt-1 mb-4">
            <div class="text-lg font-bold mb-3">评估结果</div>
            <div class="bg-gray-50 p-4 rounded-lg text-sm">
              <div class="flex justify-between mb-2">
                <span>总分：</span>
                <span class="font-medium">{{ assessmentResult.totalScore }}</span>
              </div>
              <div class="flex justify-between">
                <span>评估等级：</span>
                <span class="font-medium">{{ assessmentResult.result }}</span>
              </div>
            </div>
          </div> -->

          <!-- 评估师分析 - 仅在 templateType 为 1 或 2 时显示 -->
          <div v-if="templateType === 1 || templateType === 2" class="mt-4">
            <div class="mb-2">评估师分析</div>
            <div class="rounded-lg text-sm">
              <div class="">
                <el-input
                  type="textarea"
                  v-model="evaluatorAnalysis"
                  :rows="3"
                  placeholder="请输入评估师分析"
                />
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.eval-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 5px;

    .label {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
      font-size: 14px;
    }

    .value {
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

:deep(.el-step__title) {
  font-size: 14px;
  line-height: 25px;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

// 其他现有样式...
</style>
