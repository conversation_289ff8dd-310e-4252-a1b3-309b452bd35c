import request from '@/config/axios'

// 用药详情 VO
export interface MedicationDetailVO {
  id: number // 主键ID
  medicationId: number // 用药记录ID
  medicineName: string // 药物名称
  usageMethod: string // 服用方法
  dosage: string // 用药剂量
  frequency: string // 用药频率
  notes: string // 用药注意事项
}

// 用药详情 API
export const MedicationDetailApi = {
  // 查询用药详情分页
  getMedicationDetailPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/medication-detail/page`, params })
  },

  // 查询用药详情详情
  getMedicationDetail: async (id: number) => {
    return await request.get({ url: `/elderArchives/medication-detail/get?id=` + id })
  },

  // 新增用药详情
  createMedicationDetail: async (data: MedicationDetailVO) => {
    return await request.post({ url: `/elderArchives/medication-detail/create`, data })
  },

  // 修改用药详情
  updateMedicationDetail: async (data: MedicationDetailVO) => {
    return await request.put({ url: `/elderArchives/medication-detail/update`, data })
  },

  // 删除用药详情
  deleteMedicationDetail: async (id: number) => {
    return await request.delete({ url: `/elderArchives/medication-detail/delete?id=` + id })
  },

  // 导出用药详情 Excel
  exportMedicationDetail: async (params) => {
    return await request.download({ url: `/elderArchives/medication-detail/export-excel`, params })
  },
}
