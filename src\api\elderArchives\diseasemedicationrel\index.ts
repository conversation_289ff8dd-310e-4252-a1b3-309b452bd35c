import request from '@/config/axios'

// 疾病与用药关联 VO
export interface DiseaseMedicationRelVO {
  id: number // 主键ID
  diagnosisId: number // 诊断ID
  medicationId: number // 用药记录ID
  createTime: string // 创建时间

  // 关联的疾病诊断信息
  elderId?: number // 老人ID
  elderName?: string // 老人姓名
  diagnosisDate?: string // 诊断日期
  doctorName?: string // 诊断医生
  diagnosisType?: number // 诊断类型
  diseaseName?: string // 疾病名称

  // 关联的用药记录信息
  recordDate?: string // 记录日期
  recorderName?: string // 记录人员
  isLongTerm?: boolean // 是否长期用药
  medicineName?: string // 药物名称
}

// 疾病诊断 VO
export interface DiseaseDiagnosisVO {
  id: number // 主键ID
  elderId: number // 老人ID
  elderName?: string // 老人姓名
  diagnosisDate: string // 诊断日期
  doctorName: string // 诊断医生
  diagnosisType: number // 诊断类型
}

// 用药记录 VO
export interface MedicationRecordVO {
  id: number // 主键ID
  elderId: number // 老人ID
  elderName?: string // 老人姓名
  recordDate: string // 记录日期
  recorderName: string // 记录人员
  isLongTerm: boolean // 是否长期用药
}

// 疾病与用药关联 API
export const DiseaseMedicationRelApi = {
  // 查询疾病与用药关联分页
  getDiseaseMedicationRelPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/disease-medication-rel/page`, params })
  },

  // 查询疾病与用药关联详情
  getDiseaseMedicationRel: async (id: number) => {
    return await request.get({ url: `/elderArchives/disease-medication-rel/get?id=` + id })
  },

  // 新增疾病与用药关联
  createDiseaseMedicationRel: async (data: DiseaseMedicationRelVO) => {
    return await request.post({ url: `/elderArchives/disease-medication-rel/create`, data })
  },

  // 修改疾病与用药关联
  updateDiseaseMedicationRel: async (data: DiseaseMedicationRelVO) => {
    return await request.put({ url: `/elderArchives/disease-medication-rel/update`, data })
  },

  // 删除疾病与用药关联
  deleteDiseaseMedicationRel: async (id: number) => {
    return await request.delete({ url: `/elderArchives/disease-medication-rel/delete?id=` + id })
  },

  // 导出疾病与用药关联 Excel
  exportDiseaseMedicationRel: async (params) => {
    return await request.download({ url: `/elderArchives/disease-medication-rel/export-excel`, params })
  },

  // 获取疾病诊断列表
  getDiseaseDiagnosisList: async () => {
    return await request.get({ url: `/elderArchives/disease-diagnosis/page?pageSize=100` })
  },

  // 获取用药记录列表
  getMedicationRecordList: async () => {
    return await request.get({ url: `/elderArchives/medication-record/page?pageSize=100` })
  },
}
