/*
 * @Date: 2025-02-12 14:31:44
 * @Author: xkk
 * @LastEditTime: 2025-03-27 09:41:12
 * @FilePath: \work\huiyi_manage\src\api\evaluation\template\index.ts
 */
import request from '@/config/axios'

// 评估模板 VO
export interface TemplateVO {
  id: number // 模板ID
  name: string // 模板名称
  version: string // 版本号
  status: number // 状态
  type: number // 类型
  apiKey: string // API密钥
  formSchema: string // 模板JSON
}

// 评估模板 API
export const TemplateApi = {
  // 查询评估模板分页
  getTemplatePage: async (params: any) => {
    return await request.get({ url: `/evaluation/template/page`, params })
  },

  // 查询评估模板详情
  getTemplate: async (id: number) => {
    return await request.get({ url: `/evaluation/template/get?id=` + id })
  },

  // 新增评估模板
  createTemplate: async (data: TemplateVO) => {
    return await request.post({ url: `/evaluation/template/create`, data })
  },

  // 修改评估模板
  updateTemplate: async (data: TemplateVO) => {
    return await request.put({ url: `/evaluation/template/update`, data })
  },

  // 删除评估模板
  deleteTemplate: async (id: number) => {
    return await request.delete({ url: `/evaluation/template/delete?id=` + id })
  },

  // 导出评估模板 Excel
  exportTemplate: async (params) => {
    return await request.download({ url: `/evaluation/template/export-excel`, params })
  },

  // 获取模板的 API 密钥
  getTemplateApiKey: async (id: number) => {
    return await request.get({ url: '/evaluation/template/get-api-key?id=' + id })
  },

  // 获取评估模板精简信息列表
  getSimpleTemplateList: async () => {
    return await request.get({ url: '/evaluation/template/list-all-simple' })
  }
}
