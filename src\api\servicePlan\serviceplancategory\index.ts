import request from '@/config/axios'

// 服务计划分类 VO
export interface CategoryVO {
  id: number // 自增主键
  name: string // 分类名称
  parentId: number // 父级分类ID
  ancestors: string // 祖级列表
  level: number // 层级
  difyKey: string // Dify平台密钥
}

// 服务计划分类 API
export const CategoryApi = {

  // 查询服务计划分类详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/servicePlan/category/get?id=` + id })
  },

  // 新增服务计划分类
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/servicePlan/category/create`, data })
  },

  // 修改服务计划分类
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/servicePlan/category/update`, data })
  },

  // 删除服务计划分类
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/servicePlan/category/delete?id=` + id })
  },

  //生成服务计划分类树
  generateCategoryTree: async () => {
    return await request.get({ url: `/servicePlan/category/tree` })
  },

  // 生成简单树
  generateSimpleTree: async () => {
    return await request.get({ url: `/servicePlan/category/simple-tree` })
  },
}
