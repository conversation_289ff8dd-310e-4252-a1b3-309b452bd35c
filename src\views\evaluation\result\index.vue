<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="评估师姓名" prop="evaluatorName">
        <el-input
          v-model="queryParams.evaluatorName"
          placeholder="请输入评估师姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="评估时间" prop="evaluationTime">
        <el-date-picker
          v-model="queryParams.evaluationTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="primary"
          plain
          @click="openDialog"
          v-hasPermi="['evaluation:result:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['evaluation:result:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 新增评估弹窗 -->
    <el-dialog v-model="dialogVisible" title="新增评估" width="500px" :close-on-click-modal="false">
      <el-form :model="formData" label-width="100px" v-loading="formLoading">
        <el-form-item label="评估方式" required>
          <el-radio-group v-model="formData.type" @change="handleTypeChange">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_RESULT_TYPE)"
              :key="dict.value"
              :value="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择老人" required>
          <el-select
            v-model="formData.elderId"
            placeholder="请选择老人"
            @change="handleElderChange"
            clearable
            filterable
          >
            <el-option
              v-for="item in elderList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <!-- 单一评估模板 -->
        <el-form-item label="评估模板" required v-if="formData.type === 0">
          <el-select
            v-model="formData.templateId"
            placeholder="请选择评估模板"
            @change="handleTemplateChange"
          >
            <el-option
              v-for="item in templateList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <span :class="{ 'text-red-500': getTemplateValidityInfo(item.id).isExpired }">{{
                item.name
              }}</span>
            </el-option>
          </el-select>
          <el-alert
            v-if="templateValidityWarning.show"
            :title="templateValidityWarning.message"
            :type="templateValidityWarning.type"
            show-icon
            :closable="false"
            class="mt-2 text-xs"
          />
        </el-form-item>
        <!-- 评估任务清单 -->
        <el-form-item label="评估任务清单" required v-if="formData.type === 1">
          <el-select
            v-model="formData.listId"
            placeholder="请选择评估任务清单"
            @change="handleListChange"
            filterable
          >
            <el-option
              v-for="item in assessmentLists"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评估原因" prop="evaluationReason">
          <el-select
            v-model="evaluationReasonValue"
            multiple
            :multiple-limit="1"
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择评估原因"
            @change="handleEvaluationReasonChange"
            ref="evaluationReasonSelect"
          >
            <template #prefix>
              <el-tooltip content="仅支持选择单个选项，且支持创建新的选项" placement="top">
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-option
              v-for="item in evaluationReasonOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评估师">
          <el-input v-model="formData.evaluatorName" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="formLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加确认对话框 -->
    <el-dialog v-model="expiredConfirmVisible" title="模板已过期" width="400px">
      <span>您选择的模板已过期，是否确认继续使用？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="expiredConfirmVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUseExpiredTemplate"> 确认使用 </el-button>
        </span>
      </template>
    </el-dialog>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!--<el-table-column label="结果ID" align="center" prop="id" />-->
      <el-table-column label="模板名称" align="center" prop="templateName" min-width="300" />
      <el-table-column label="老人姓名" align="center" prop="elderName" width="150" />
      <el-table-column label="评估方式" align="center" prop="type" width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATION_RESULT_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="评估师姓名" align="center" prop="evaluatorName" width="150" />
      <el-table-column label="评估原因" align="center" prop="evaluationReason" width="150" />
      <el-table-column
        label="评估时间"
        align="center"
        prop="evaluationTime"
        :formatter="dateFormatter"
        width="250"
      />
      <el-table-column label="有效期状态" align="center" width="200">
        <template #default="scope">
          <el-tag :type="isResultExpired(scope.row) ? 'danger' : 'success'" effect="light">
            {{ getValidityTip(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="300px">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)">查看详情</el-button>
          <!-- 只对非复评记录且未被复评过的记录显示复评按钮 -->
          <el-button
            v-if="
              scope.row.evaluationReason !== '因对评估结果有疑问进行的复评' &&
              !hasReEvaluation[scope.row.id]
            "
            link
            type="warning"
            @click="handleReEvaluation(scope.row)"
          >
            复评
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['evaluation:result:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ResultForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ResultApi, ResultVO } from '@/api/evaluation/result'
import { TemplateApi, TemplateVO } from '@/api/evaluation/template'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile'
import { ListApi, ListVO } from '@/api/evaluation/list'
import { ListExecutionApi } from '@/api/evaluation/listexecution'
import ResultForm from './ResultForm.vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { InfoFilled } from '@element-plus/icons-vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'

defineOptions({ name: 'Result' })

//
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const userStore = useUserStore()

// 评估任务清单列表
const assessmentLists = ref<ListVO[]>([])
// 评估原因
const evaluationReasonValue = ref<string[]>([])
// 评估原因选项
const evaluationReasonOptions = [
  {
    value: '首次评估',
    label: '首次评估'
  },
  {
    value: '常规评估',
    label: '常规评估'
  },
  {
    value: '即时评估',
    label: '即时评估'
  }
]

// 列表的加载中
const loading = ref(true)
// 列表的数据
const list = ref<ResultVO[]>([])
// 列表的总页数
const total = ref(0)
// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  elderName: undefined,
  templateId: undefined,
  templateName: undefined,
  evaluatorId: undefined,
  evaluatorName: undefined,
  evaluationTime: [],
  result: undefined,
  createTime: []
})
// 搜索的表单
const queryFormRef = ref()
// 导出的加载中
const exportLoading = ref(false)

// 新增评估相关状态
const dialogVisible = ref(false)
// 新增评估的加载中
const formLoading = ref(false)
// 新增评估的表单数据
const formData = reactive({
  elderId: undefined as number | undefined,
  elderName: '',
  templateId: undefined as number | undefined,
  templateName: '',
  listId: undefined as number | undefined,
  listName: '',
  requiredTemplateIds: '',
  evaluatorId: undefined as number | undefined,
  evaluatorName: '',
  evaluationReason: '',
  type: 0
})

// 模板列表
const templateList = ref<TemplateVO[]>([])
// 老人列表
const elderList = ref<ArchivesProfileVO[]>([])

// 添加一个计算属性来判断是否已经有复评记录
const hasReEvaluation = ref<{ [key: number]: boolean }>({})

// 添加一个变量用于显示模板有效期警告
const templateValidityWarning = ref({
  show: false,
  message: '',
  type: 'warning' as 'success' | 'warning' | 'info' | 'error'
})

// 添加用于确认使用过期模板的对话框
const expiredConfirmVisible = ref(false)
// 过期模板ID
const expiredTemplateId = ref<number | null>(null)

// 添加 ref 用于操作 select 组件
const evaluationReasonSelect = ref()

// 处理评估类型变化
const handleEvaluationReasonChange = () => {
  // 如果选择了一个选项，就关闭下拉框
  if (evaluationReasonValue.value.length === 1) {
    evaluationReasonSelect.value.blur()
  }
}

// 获取模板列表
const getTemplateList = async () => {
  try {
    const res = await TemplateApi.getTemplatePage({
      pageNo: 1,
      pageSize: 100,
      status: 0
    })
    templateList.value = res.list
  } catch (error) {
    // console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  }
}

// 获取老人列表
const getElderList = async () => {
  try {
    const res = await ArchivesProfileApi.getArchivesProfilePage({
      pageNo: 1,
      pageSize: 100
    })
    elderList.value = res.list
  } catch (error) {
    // console.error('获取老人列表失败:', error)
    message.error('获取老人列表失败')
  }
}

// 老人选择变化时
const handleElderChange = (elderId: number) => {
  const elder = elderList.value.find((e) => e.id === elderId)
  if (elder) {
    formData.elderName = elder.name
  }
}

// 获取评估任务清单列表
const getAssessmentLists = async () => {
  try {
    const res = await ListApi.getListPage({
      pageNo: 1,
      pageSize: 100,
      status: 0 // 假设 0 表示启用状态
    })
    assessmentLists.value = res.list
  } catch (error) {
    // console.error('获取评估任务清单失败:', error)
    message.error('获取评估任务清单失败')
  }
}

// 处理评估方式变更
const handleTypeChange = () => {
  // 切换评估方式时清空之前的选择
  formData.templateId = undefined
  formData.templateName = ''
  formData.listId = undefined
  formData.listName = ''
  formData.requiredTemplateIds = ''
  templateValidityWarning.value.show = false

  // 如果切换到评估任务清单，获取评估任务清单列表
  if (formData.type === 1) {
    getAssessmentLists()
  }
}

// 处理评估任务清单选择变化
const handleListChange = (listId: number) => {
  const list = assessmentLists.value.find((l) => l.id === listId)
  if (list) {
    formData.listName = list.name
    formData.requiredTemplateIds = list.templateIds
  }
}

// 获取复评记录信息
const getReEvaluationInfo = async (list: ResultVO[]) => {
  try {
    // 重置状态
    hasReEvaluation.value = {}

    // 如果列表为空，直接返回
    if (!list || list.length === 0) return

    // 从当前列表找出所有复评记录
    const reEvaluations = list.filter(
      (item) => item.evaluationReason === '因对评估结果有疑问进行的复评' && item.connectResultId
    )

    // 如果有复评记录，则找出被复评过的原记录
    reEvaluations.forEach((reEval) => {
      if (reEval.connectResultId) {
        hasReEvaluation.value[reEval.connectResultId] = true
      }
    })
  } catch (error) {
    // console.error('处理复评信息失败:', error)
    message.error('处理复评信息失败')
  }
}

// 获取模板有效期状态信息
const getTemplateValidityInfo = (templateId: number | null) => {
  if (!templateId) {
    return {
      message: '请选择评估模板',
      isExpired: false,
      daysLeft: 0
    }
  }

  const template = templateList.value.find((t) => t.id === templateId)
  if (!template || !template.formSchema) {
    return {
      message: '无法获取模板有效期信息',
      isExpired: false,
      daysLeft: 0
    }
  }

  try {
    // 解析表单模式
    const schema =
      typeof template.formSchema === 'string'
        ? JSON.parse(template.formSchema)
        : template.formSchema

    // 获取有效期信息
    const validityPeriod = schema.option?.form?.validityPeriod || 3 // 默认3
    const validityUnit = schema.option?.form?.validityUnit || 'month' // 默认月
    const validityStartTimeType = schema.option?.form?.validityStartTimeType || 'evaluationTime' // 默认为评估时间
    const validityStartTime = schema.option?.form?.validityStartTime || '' // 固定日期

    // 计算剩余天数
    const today = new Date()
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数

    // 确定起始日期
    let startDate
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      // 如果是固定日期模式且有设置日期
      startDate = new Date(validityStartTime)
    } else {
      // 默认使用今天的日期
      startDate = today
    }

    // 根据单位计算到期日期
    let expiryDate = new Date(startDate)

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    // 生成有效期文本
    let validityText = `${validityPeriod}`
    switch (validityUnit) {
      case 'day':
        validityText += validityPeriod > 1 ? '天' : '天'
        break
      case 'week':
        validityText += validityPeriod > 1 ? '周' : '周'
        break
      case 'month':
      default:
        validityText += validityPeriod > 1 ? '个月' : '个月'
        break
    }

    let startInfo = ''
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      startInfo = `，从${validityStartTime}起`
    }

    // 根据起始时间类型返回不同的提示
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      if (daysLeft <= 0) {
        return {
          message: `模板有效期已过期 (有效期${validityText}${startInfo})`,
          isExpired: true,
          daysLeft
        }
      }
      return {
        message: `模板剩余有效期: ${daysLeft} 天 (有效期${validityText}${startInfo})`,
        isExpired: false,
        daysLeft
      }
    } else {
      return {
        message: `模板有效期: ${validityText} (从评估时间起计算)`,
        isExpired: false,
        daysLeft: 999 // 不适用固定期限
      }
    }
  } catch (error) {
    // console.error('解析有效期信息失败:', error)
    return {
      message: '无法获取模板有效期信息',
      isExpired: false,
      daysLeft: 0
    }
  }
}

// 模板选择变化时
const handleTemplateChange = async (templateId: number) => {
  const template = templateList.value.find((t) => t.id === templateId)
  if (template) {
    formData.templateName = template.name

    // 检查模板是否过期
    const validityInfo = getTemplateValidityInfo(templateId)

    // 更新警告信息
    if (validityInfo.isExpired) {
      templateValidityWarning.value = {
        show: true,
        message: `警告：${validityInfo.message}`,
        type: 'error'
      }

      // 存储当前选择的模板ID
      expiredTemplateId.value = templateId

      // 弹出确认对话框
      expiredConfirmVisible.value = true

      // 重置选择，等待用户确认
      formData.templateId = undefined
    } else {
      templateValidityWarning.value = {
        show: true,
        message: validityInfo.message,
        type: validityInfo.daysLeft <= 7 ? 'warning' : 'info'
      }
    }
  }
}

// 确认使用已过期模板
const confirmUseExpiredTemplate = () => {
  if (expiredTemplateId.value) {
    formData.templateId = expiredTemplateId.value
    const template = templateList.value.find((t) => t.id === expiredTemplateId.value)
    if (template) {
      formData.templateName = template.name
    }
  }
  expiredConfirmVisible.value = false
}

// 更新计算评估结果是否过期的函数
const isResultExpired = (row: ResultVO) => {
  try {
    // 获取评估时间
    const evaluationTime = new Date(row.evaluationTime).getTime()
    if (!evaluationTime) return false

    // 获取模板中设置的有效期和单位
    let validityPeriod = 3 // 默认3
    let validityUnit = 'month' // 默认月
    let validityStartTimeType = 'evaluationTime' // 默认为评估时间
    let validityStartTime = '' // 固定起始日期

    if (row.result) {
      const resultData = JSON.parse(row.result)

      // 尝试从元数据中获取有效期设置
      if (resultData.fullResult?.metadata?.templateInfo) {
        const templateInfo = resultData.fullResult.metadata.templateInfo
        validityPeriod = templateInfo.validityPeriod || validityPeriod
        validityUnit = templateInfo.validityUnit || validityUnit
        validityStartTimeType = templateInfo.validityStartTimeType || validityStartTimeType
        validityStartTime = templateInfo.validityStartTime || validityStartTime
      }
      // 尝试从旧格式获取有效期设置
      else if (resultData.options?.form) {
        validityPeriod = resultData.options.form.validityPeriod || validityPeriod
        validityUnit = resultData.options.form.validityUnit || validityUnit
        validityStartTimeType =
          resultData.options.form.validityStartTimeType || validityStartTimeType
        validityStartTime = resultData.options.form.validityStartTime || validityStartTime
      }
    }

    // 确定起始日期
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数
    let startDate

    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      // 如果是固定日期模式且有设置日期，使用该固定日期作为起点
      startDate = new Date(validityStartTime)
    } else {
      // 默认使用评估时间作为起点
      startDate = new Date(evaluationTime)
    }

    // 计算过期时间
    let expiryDate = new Date(startDate)

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 检查是否已过期
    const today = new Date()
    return today > expiryDate
  } catch (error) {
    // console.error('计算评估结果是否过期失败:', error, row)
    return false
  }
}

// 修改获取有效期提示文本的函数
const getValidityTip = (row: ResultVO) => {
  try {
    // 获取评估时间
    const evaluationTime = new Date(row.evaluationTime).getTime()
    if (!evaluationTime) return '未知有效期'

    // 获取模板中设置的有效期和单位
    let validityPeriod = 3 // 默认3
    let validityUnit = 'month' // 默认月
    let validityStartTimeType = 'evaluationTime' // 默认为评估时间
    let validityStartTime = '' // 固定起始日期

    if (row.result) {
      const resultData = JSON.parse(row.result)

      // 尝试从元数据中获取有效期设置
      if (resultData.fullResult?.metadata?.templateInfo) {
        const templateInfo = resultData.fullResult.metadata.templateInfo
        validityPeriod = templateInfo.validityPeriod || validityPeriod
        validityUnit = templateInfo.validityUnit || validityUnit
        validityStartTimeType = templateInfo.validityStartTimeType || validityStartTimeType
        validityStartTime = templateInfo.validityStartTime || validityStartTime
      }
      // 尝试从旧格式获取有效期设置
      else if (resultData.options?.form) {
        validityPeriod = resultData.options.form.validityPeriod || validityPeriod
        validityUnit = resultData.options.form.validityUnit || validityUnit
        validityStartTimeType =
          resultData.options.form.validityStartTimeType || validityStartTimeType
        validityStartTime = resultData.options.form.validityStartTime || validityStartTime
      }
    }

    // 确定起始日期
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数
    let startDate

    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      // 如果是固定日期模式且有设置日期，使用该固定日期作为起点
      startDate = new Date(validityStartTime)
    } else {
      // 默认使用评估时间作为起点
      startDate = new Date(evaluationTime)
    }

    // 计算过期时间
    let expiryDate = new Date(startDate)

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const today = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    // 生成有效期文本
    let validityText = `${validityPeriod}`
    switch (validityUnit) {
      case 'day':
        validityText += validityPeriod > 1 ? '天' : '天'
        break
      case 'week':
        validityText += validityPeriod > 1 ? '周' : '周'
        break
      case 'month':
      default:
        validityText += validityPeriod > 1 ? '个月' : '个月'
        break
    }

    // 添加起始时间信息
    let startInfo = ''
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      startInfo = `，从 ${validityStartTime} 起`
    }

    if (daysLeft <= 0) {
      return `已过期 (有效期${validityText}${startInfo})`
    } else {
      return `剩余${daysLeft}天(有效期${validityText}${startInfo})`
    }
  } catch (error) {
    // console.error('计算有效期提示失败:', error, row)
    return '未知有效期'
  }
}

// 打开弹窗
const openDialog = () => {
  // 重置表单数据
  formData.elderId = undefined
  formData.elderName = ''
  formData.templateId = undefined
  formData.templateName = ''
  formData.listId = undefined
  formData.listName = ''
  formData.evaluatorId = userStore.user.id
  formData.evaluatorName = userStore.user.nickname
  formData.type = 0 // 默认选择单一评估模板
  // formData.evaluationTime = new Date().getTime()
  evaluationReasonValue.value = []
  dialogVisible.value = true
  templateValidityWarning.value.show = false
  // 获取所有需要的数据
  getTemplateList()
  getElderList()
}

// 确认创建评估
const handleConfirm = async () => {
  if (!formData.elderId) {
    message.error('请选择老人')
    return
  }

  if (!evaluationReasonValue.value.length) {
    message.error('请选择评估原因')
    return
  }

  if (formData.type === 0) {
    // 验证模板选择
    if (!formData.templateId) {
      message.error('请选择评估模板')
      return
    }

    // 跳转到单一模板评估页面
    router.push({
      path: '/evaluation/aiEval',
      query: {
        elderId: formData.elderId,
        templateId: formData.templateId,
        templateName: formData.templateName,
        evaluatorId: formData.evaluatorId,
        evaluatorName: formData.evaluatorName,
        evaluationReason: evaluationReasonValue.value[0],
        type: formData.type
        // evaluationTime: formData.evaluationTime
      }
    })
  } else {
    // 验证评估任务清单选择
    if (!formData.listId) {
      message.error('请选择评估任务清单')
      return
    }

    // 创建评估任务清单执行记录，然后跳转到新的步骤式评估页面
    try {
      formLoading.value = true

      // 创建评估任务清单执行记录
      const executionData = {
        listId: formData.listId,
        listName: formData.listName,
        elderId: formData.elderId,
        elderName: formData.elderName,
        evaluatorId: formData.evaluatorId,
        evaluatorName: formData.evaluatorName,
        evaluationReason: evaluationReasonValue.value[0],
        status: 0,
        startTime: new Date().getTime(),
        requiredTemplateIds: formData.requiredTemplateIds
      }

      const result = await ListExecutionApi.createListExecution(executionData)

      // 跳转到步骤式评估页面
      router.push({
        path: '/evaluation/listEval',
        query: {
          id: result
        }
      })
    } catch (error) {
      // console.error('创建评估任务清单执行记录失败:', error)
      message.error('创建评估任务清单执行记录失败')
    } finally {
      formLoading.value = false
    }
  }

  dialogVisible.value = false
}

/** 查看详情按钮操作 */
const handleDetail = (row: ResultVO) => {
  router.push({
    path: '/evaluation/aiEvalDetail',
    query: {
      id: row.id
    }
  })
}

/** 处理复评操作 */
const handleReEvaluation = (row: ResultVO) => {
  // 跳转到评估详情页面，带上复评标记和必要参数
  router.push({
    path: '/evaluation/aiEvalDetail',
    query: {
      id: row.id,
      isReEvaluation: 'true',
      evaluatorId: userStore.user.id,
      evaluatorName: userStore.user.nickname
    }
  })
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ResultApi.getResultPage(queryParams)
    list.value = data.list
    total.value = data.total
    // 获取复评记录信息
    await getReEvaluationInfo(data.list)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
// const openForm = (type: string, id?: number) => {
//   formRef.value.open(type, id)
// }

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ResultApi.deleteResult(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ResultApi.exportResult(queryParams)
    download.excel(data, '评估结果.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  min-width: 100px;
}
</style>
