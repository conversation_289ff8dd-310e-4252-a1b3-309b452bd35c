import request from '@/config/axios'

// 服务任务 VO
export interface TaskVO {
  id: number // 自增主键
  planId: number // 关联护理计划ID
  elderId: number // 关联老人表
  taskType: string // 任务类型：清洗/喂药/康复训练
  status: number // 状态：1-待分配 2-进行中 3-已完成 4-已取消
  isRecurring: number // 是否周期任务: 0-否 1-是
  repeatPattern: string // 周期性规则（JSON格式）
  startTime: string // 任务开始时间点
  endTime: string // 任务结束时间点
  recommendedExecutors: string // 推荐的执行人信息（JSON数组格式）
  priority: number // 优先级：1-紧急 2-高 3-中 4-低
  description: string // 执行细则
}

// 服务任务 API
export const TaskApi = {
  // 查询服务任务分页
  getTaskPage: async (params: any) => {
    return await request.get({url: `/servicePlan/task/page`, params})
  },

  // 查询服务任务详情
  getTask: async (id: number) => {
    return await request.get({url: `/servicePlan/task/get?id=` + id})
  },

  // 新增服务任务
  createTask: async (data: TaskVO) => {
    return await request.post({url: `/servicePlan/task/create`, data})
  },

  // 修改服务任务
  updateTask: async (data: TaskVO) => {
    return await request.put({url: `/servicePlan/task/update`, data})
  },

  // 删除服务任务
  deleteTask: async (id: number) => {
    return await request.delete({url: `/servicePlan/task/delete?id=` + id})
  },

  // 导出服务任务 Excel
  exportTask: async (params) => {
    return await request.download({url: `/servicePlan/task/export-excel`, params})
  },

  /** 更新任务状态 */
  updateTaskStatus: async (taskId: number, status: number) => {
    return await request.put({
      url: `/servicePlan/task/update-status/${taskId}`,
      data: status
    })
  },

  // 导出服务任务执行表 Excel
  exportTaskExecutionSheet: async (params) => {
    return await request.download({url: `/servicePlan/task/export-execution-sheet`, params})
  },

  // 获取任务简单列表
  getTaskSimpleList: async () => {
    return await request.get({url: `/servicePlan/task/simple-list`})
  }
}
