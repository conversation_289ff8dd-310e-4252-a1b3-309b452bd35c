import request from '@/config/axios'

// 服务任务分类 VO
export interface TaskCategoryVO {
  id: number // 自增主键
  name: string // 分类名称
  parentId: number // 父级分类ID
  ancestors: string // 祖级列表
  level: number // 层级
  difyKey: string // Dify平台密钥
}

// 服务任务分类 API
export const TaskCategoryApi = {

  // 查询服务任务分类详情
  getTaskCategory: async (id: number) => {
    return await request.get({ url: `/servicePlan/task-category/get?id=` + id })
  },

  // 新增服务任务分类
  createTaskCategory: async (data: TaskCategoryVO) => {
    return await request.post({ url: `/servicePlan/task-category/create`, data })
  },

  // 修改服务任务分类
  updateTaskCategory: async (data: TaskCategoryVO) => {
    return await request.put({ url: `/servicePlan/task-category/update`, data })
  },

  // 删除服务任务分类
  deleteTaskCategory: async (id: number) => {
    return await request.delete({ url: `/servicePlan/task-category/delete?id=` + id })
  },

  // 添加生成树形结构的方法
  generateTaskCategoryTree: async () => {
    return await request.get({ url: `/servicePlan/task-category/tree` })
  },

  // 添加生成简单树的方法
  generateSimpleTree: async () => {
    return await request.get({ url: `/servicePlan/task-category/simple-tree` })
  }

}
