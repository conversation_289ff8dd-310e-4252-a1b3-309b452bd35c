<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="评估任务清单名称" prop="listName">
        <el-input
          v-model="queryParams.listName"
          placeholder="请输入评估任务清单名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input
          v-model="queryParams.elderName"
          placeholder="请输入老人姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="评估师姓名" prop="evaluatorName">
        <el-input
          v-model="queryParams.evaluatorName"
          placeholder="请输入评估师姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="评估原因" prop="evaluationReason">
        <el-input
          v-model="queryParams.evaluationReason"
          placeholder="请输入评估原因"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_LIST_EXECUTION_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="queryParams.startTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="完成时间" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['evaluation:list-execution:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="ID" align="center" prop="id" /> -->
      <el-table-column label="评估任务清单名称" align="center" prop="listName" />
      <el-table-column label="老人姓名" align="center" prop="elderName" />
      <el-table-column label="评估师姓名" align="center" prop="evaluatorName" />
      <el-table-column label="评估原因" align="center" prop="evaluationReason" width="100px" />
      <el-table-column label="状态" align="center" prop="status" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATION_LIST_EXECUTION_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="完成时间"
        align="center"
        prop="endTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="所需完成的评估模板" align="center" prop="requiredTemplateIds">
        <template #default="scope">
          {{ formatTemplateNames(scope.row.requiredTemplateIds) }}
        </template>
      </el-table-column>
      <el-table-column label="已完成的评估模板" align="center" prop="completedTemplateIds">
        <template #default="scope">
          {{ formatTemplateNames(scope.row.completedTemplateIds) }}
        </template>
      </el-table-column>
      <el-table-column label="综合评估结果" align="center" prop="summaryResult" />
      <el-table-column label="操作" align="center" min-width="200px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['evaluation:list-execution:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleViewEval(scope.row.id)"
            v-if="scope.row.status === 1"
          >
            查看
          </el-button>
          <el-button
            link
            type="success"
            @click="handleContinueEval(scope.row.id)"
            v-if="scope.row.status !== 1"
          >
            继续
          </el-button>

          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['evaluation:list-execution:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ListExecutionForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'
import ListExecutionForm from './ListExecutionForm.vue'
import { useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import { useI18n } from 'vue-i18n'
import { TemplateApi } from '@/api/evaluation/template'

defineOptions({ name: 'ListExecution' })

// 国际化
const { t } = useI18n()
// 消息弹窗
const message = useMessage()
// 路由
const router = useRouter()
// 列表的加载中
const loading = ref(true)
// 列表的数据
const list = ref<ListExecutionVO[]>([])
// 列表的总页数
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  listId: undefined,
  listName: undefined,
  elderId: undefined,
  elderName: undefined,
  evaluatorId: undefined,
  evaluatorName: undefined,
  evaluationReason: undefined,
  status: undefined,
  startTime: [],
  endTime: [],
  requiredTemplateIds: undefined,
  completedTemplateIds: undefined,
  resultIds: undefined,
  apiKey: undefined,
  evaluatorAnalysis: undefined,
  aiInputs: undefined,
  aiAnalysis: undefined,
  summaryResult: undefined,
  createTime: []
})
// 搜索的表单
const queryFormRef = ref()
// 导出的加载中
const exportLoading = ref(false)
// 模板ID到名称的映射
const templateMap = ref<Record<string, string>>({})

/** 获取模板列表 */
const getTemplateOptions = async () => {
  try {
    const res = await TemplateApi.getSimpleTemplateList()
    if (res && Array.isArray(res)) {
      // 创建ID到名称的映射
      templateMap.value = res.reduce((acc, item) => {
        acc[item.id] = item.name
        return acc
      }, {})
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取模板列表
    await getTemplateOptions()

    const data = await ListExecutionApi.getListExecutionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 模板名称格式化方法 */
const formatTemplateNames = (templateIds: string) => {
  if (!templateIds) return '-'
  const ids = templateIds.split(',')
  return ids.map((id) => templateMap.value[id] || id).join(', ')
}

/** 继续评估操作 */
const handleContinueEval = (id: number) => {
  router.push({ path: '/evaluation/listEval', query: { id: id } })
}

/** 查看评估操作 */
const handleViewEval = (id: number) => {
  router.push({ path: '/evaluation/listEvalDetail', query: { id: id, readonly: 'true' } })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ListExecutionApi.deleteListExecution(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ListExecutionApi.exportListExecution(queryParams)
    download.excel(data, '评估任务清单执行.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
