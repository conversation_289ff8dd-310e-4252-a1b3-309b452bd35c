<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'

defineOptions({ name: 'ListEvalDetail' })

import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'
import { TemplateApi } from '@/api/evaluation/template'
import { useMessage } from '@/hooks/web/useMessage'
import AiEvalDetail from '@/views/evaluation/aiEval/aiEvalDetail.vue'

const route = useRoute()
const message = useMessage()

const id = route.query.id

// 评估任务清单执行数据
const listExecutionInfo = ref<ListExecutionVO | null>(null)

// 定义模板类型接口
interface TemplateItem {
  id: number | string
  name: string
}

// 模板列表
const templateList = ref<TemplateItem[]>([])

// 存储已完成的模板ID和结果ID数组
const templateIdsArray = ref<string[]>([])
const resultIdsArray = ref<string[]>([])

// 当前选中的结果ID
const currentResultId = ref('')

// 获取步骤条的当前激活步骤
const activeStep = ref(0)

// 添加加载状态
const loading = ref(true)

// 获取列表执行详情
const getListExecutionDetail = async () => {
  try {
    if (!id) {
      message.error('未提供评估任务清单执行ID')
      return
    }

    loading.value = true

    // 获取模板列表
    const templates = await TemplateApi.getSimpleTemplateList()
    templateList.value = templates
    // console.log('模板列表数据:', templateList.value)

    // 获取评估任务清单执行详情
    const data = await ListExecutionApi.getListExecution(Number(id))
    listExecutionInfo.value = data

    // 解析模板ID和结果ID
    if (data.requiredTemplateIds) {
      templateIdsArray.value = data.requiredTemplateIds.split(',')
      // console.log('模板ID数组:', templateIdsArray.value)
    }

    if (data.resultIds) {
      resultIdsArray.value = data.resultIds.split(',')
      if (resultIdsArray.value.length > 0) {
        // 默认选中第一个结果
        currentResultId.value = resultIdsArray.value[0]
        activeStep.value = 0
      }
    }
  } catch (error) {
    // console.error('获取评估任务清单执行详情失败:', error)
    message.error('获取评估任务清单执行详情失败')
  } finally {
    loading.value = false
  }
}

// 获取模板名称
const getTemplateName = (templateId: string) => {
  // 添加更多的调试信息
  // console.log(`查找模板 ID: ${templateId}，类型: ${typeof templateId}`)
  // console.log('所有模板:', templateList.value)

  // 将两边都转为字符串进行比较
  const template = templateList.value.find((t) => String(t.id) === String(templateId))

  if (template) {
    // console.log(`找到模板: ${template.name}`)
    return template.name
  } else {
    // console.log(`未找到模板 ${templateId}`)
    ElMessage.error(`未找到模板 ${templateId}`)
    return `模板 ${templateId}`
  }
}

// 切换步骤
const handleStepChange = (index: number) => {
  if (index < resultIdsArray.value.length) {
    // 更新当前步骤和结果ID
    currentResultId.value = resultIdsArray.value[index]
    // console.log('当前索引:', index)
    activeStep.value = index

    // 滚动到当前步骤
    nextTick(() => {
      const stepElement = document.querySelector(`.el-step:nth-child(${index + 1})`)
      if (stepElement && stepElement.parentElement) {
        stepElement.parentElement.scrollTop = (stepElement as HTMLElement).offsetTop - 120
      }
    })
  }
}

// 上一个评估结果
const handlePrev = () => {
  if (activeStep.value > 0) {
    handleStepChange(activeStep.value - 1)
  }
}

// 下一个评估结果
const handleNext = () => {
  if (activeStep.value < resultIdsArray.value.length - 1) {
    handleStepChange(activeStep.value + 1)
  }
}

// 初始化
onMounted(() => {
  loading.value = true // 开始加载，设置加载状态为true
  getListExecutionDetail()
})
</script>

<template>
  <div class="flex flex-col h-full" v-loading="loading" element-loading-text="数据加载中...">
    <div class="flex flex-row" v-if="!loading">
      <!-- 左侧步骤条 -->
      <div class="w-40 mr-2">
        <el-card class="h-full left-nav-card">
          <template #header>
            <div class="card-header">
              <span class="font-bold">评估任务清单</span>
              <span class="text-gray-500 text-sm">{{ listExecutionInfo?.listName }}</span>
            </div>
          </template>

          <div class="steps-container">
            <el-steps direction="vertical" :active="activeStep">
              <el-step
                v-for="(templateId, index) in templateIdsArray"
                :key="templateId"
                :title="getTemplateName(templateId)"
                :class="{ 'current-step': index === activeStep }"
              >
                <template #icon>
                  <div
                    class="step-icon cursor-pointer"
                    @click="index < resultIdsArray.length ? handleStepChange(index) : null"
                    :class="{
                      'step-active': index === activeStep,
                      'step-completed': index < resultIdsArray.length,
                      'step-disabled': index >= resultIdsArray.length
                    }"
                  >
                    {{ index + 1 }}
                  </div>
                </template>
              </el-step>
            </el-steps>
          </div>

          <!-- 添加上一个、下一个按钮 -->
          <div class="mt-4 flex justify-between navigation-buttons">
            <el-button
              type="primary"
              :disabled="activeStep <= 0"
              @click="handlePrev"
              plain
              class="prev-btn"
            >
              <el-icon class="mr-1"><ArrowLeft /></el-icon
            ></el-button>
            <el-button
              type="primary"
              :disabled="activeStep >= resultIdsArray.length - 1 || resultIdsArray.length === 0"
              @click="handleNext"
              plain
              class="next-btn"
              ><el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧评估详情 -->
      <div class="flex-1">
        <transition name="fade" mode="out-in">
          <AiEvalDetail v-if="currentResultId" :resultId="currentResultId" :key="currentResultId" />
          <div v-else class="flex items-center justify-center h-full">
            <el-empty description="请选择一个评估结果查看详情" />
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;

  &.step-active {
    background-color: var(--el-color-primary);
    color: white;
    font-weight: bold;
  }

  &.step-completed {
    background-color: var(--el-color-success);
    color: white;
  }

  &.step-disabled {
    background-color: var(--el-color-info-light-5);
    color: var(--el-color-info);
    cursor: not-allowed;
  }

  &:hover:not(.step-disabled) {
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: scale(0.6);
  }
  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}

/* 左侧导航卡片特定样式 */
.left-nav-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  font-size: 14px;

  :deep(.el-card__header) {
    padding: 14px 16px;
    // border-bottom: 1px solid var(--el-border-color-light);
    // background-color: var(--el-fill-color-light);
    border-radius: 6px 6px 0 0;
  }

  :deep(.el-card__body) {
    // padding: 16px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 70px);
    overflow-y: auto;
  }

  :deep(.el-steps) {
    flex: 1;
  }

  :deep(.el-step) {
    margin-bottom: 8px;

    &.is-active {
      .el-step__title {
        font-weight: bold;
        color: var(--el-color-primary);
      }

      .el-step__description {
        color: var(--el-color-primary-light-3);
      }

      &::before {
        content: '';
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: var(--el-color-primary);
        border-radius: 0 3px 3px 0;
      }

      // 添加背景色
      .el-step__main {
        background-color: rgba(64, 158, 255, 0.08);
        padding: 8px 12px;
        border-radius: 4px;
        margin-left: -8px;
        transition: all 0.3s;
      }
    }
  }

  :deep(.el-step__title) {
    font-size: 14px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    padding-right: 10px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  :deep(.el-step__description) {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 2px;
    transition: all 0.3s ease;
  }

  :deep(.el-step__line) {
    background-color: #e4e7ed;

    &.is-finish {
      background-color: var(--el-color-success);
    }
  }

  :deep(.el-step.is-vertical .el-step__line) {
    left: 13px;
    top: 6px;
    height: calc(100% - 8px);
  }
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.steps-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 16px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #909399;
  }
}

.navigation-buttons {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed var(--el-border-color-light);

  .prev-btn,
  .next-btn {
    // min-width: 90px;
    transition: all 0.2s ease;

    &:not([disabled]):hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }
}

.current-step {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: var(--el-color-primary);
    border-radius: 0 2px 2px 0;
    z-index: 5;
  }

  // 放大图标
  .step-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
}

/* 内容区域的过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
