<template>
  <div class="markdown-editor-wrapper">
    <!-- AI 生成按钮和思考过程容器 -->
    <div class="description-actions" :class="{ 'inline-mode': isCollapsed }">
      <el-button 
        type="primary" 
        @click="handleGenerate" 
        :disabled="aiButtonDisabled || isThinking"
        class="ai-button"
      >
        <el-icon><MagicStick /></el-icon>
        <span class="ml-1">AI 生成描述</span>
      </el-button>

      <!-- AI 思考过程 -->
      <el-collapse 
        v-show="aiReasoning && aiReasoning.length > 0" 
        v-model="activeCollapse"
        class="ai-reasoning-collapse"
      >
        <el-collapse-item name="1">
          <template #title>
            <div class="collapse-title">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
              <span>AI 思考过程</span>
            </div>
          </template>
          <div class="ai-reasoning-content">
            <div class="reasoning-text">{{ aiReasoning }}</div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <el-button-group>
        <el-tooltip content="标题 1" placement="top">
          <el-button @click="insertMarkdown('# ')">H1</el-button>
        </el-tooltip>
        <el-tooltip content="标题 2" placement="top">
          <el-button @click="insertMarkdown('## ')">H2</el-button>
        </el-tooltip>
        <el-tooltip content="标题 3" placement="top">
          <el-button @click="insertMarkdown('### ')">H3</el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <el-button-group>
        <el-tooltip content="粗体" placement="top">
          <el-button @click="wrapSelection('**', '**')">
            <el-icon><Edit /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="斜体" placement="top">
          <el-button @click="wrapSelection('*', '*')">
            <el-icon><EditPen /></el-icon>
          </el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <el-button-group>
        <el-tooltip content="无序列表" placement="top">
          <el-button @click="insertMarkdown('- ')">
            <el-icon><List /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="有序列表" placement="top">
          <el-button @click="insertMarkdown('1. ')">
            <el-icon><Sort /></el-icon>
          </el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <el-tooltip content="预览模式" placement="top">
        <el-button @click="togglePreview">
          <el-icon><View /></el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <!-- 编辑器主体 -->
    <div class="markdown-editor">
      <div v-show="!isPreview" class="editor-container">
        <el-input
          ref="textareaRef"
          :model-value="localValue"
          @update:model-value="handleInput"
          type="textarea"
          :rows="12"
          :placeholder="placeholder"
          resize="none"
          :disabled="isThinking"
        />
      </div>
      <div v-show="isPreview" class="preview-container">
        <div class="markdown-content" v-html="renderMarkdown(localValue || '')"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { MagicStick, InfoFilled, Edit, EditPen, List, Sort, View } from '@element-plus/icons-vue'
import MarkdownIt from 'markdown-it'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入 Markdown 格式的内容'
  },
  showAiButton: {
    type: Boolean,
    default: true
  },
  aiButtonDisabled: {
    type: Boolean,
    default: false
  },
  aiReasoning: {
    type: String,
    default: ''
  },
  isThinking: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'generate'])

const activeCollapse = ref(['1'])

// 创建本地响应式变量来代理 modelValue
const localValue = ref(props.modelValue)

// 监听 prop 变化，更新本地值
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue
})

// 监听 isThinking 变化，当 AI 思考完成后自动折叠
watch(() => props.isThinking, (newValue) => {
  if (!newValue && props.aiReasoning) {
    activeCollapse.value = []
  }
})

// 初始化 markdown-it
const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true
})

// 转换 Markdown 为 HTML
const renderMarkdown = (content: string) => {
  return md.render(content)
}

// 处理输入
const handleInput = (value: string) => {
  emit('update:modelValue', value)
}

// 处理 AI 生成
const handleGenerate = () => {
  emit('generate')
}

// 计算折叠状态
const isCollapsed = computed(() => activeCollapse.value.length === 0)

const isPreview = ref(false)
const textareaRef = ref()

// 切换预览模式
const togglePreview = () => {
  isPreview.value = !isPreview.value
}

// 在光标位置插入 Markdown 语法
const insertMarkdown = (syntax: string) => {
  const textarea = textareaRef.value.$el.querySelector('textarea')
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const text = localValue.value || ''
  
  const newValue = text.substring(0, start) + syntax + text.substring(end)
  emit('update:modelValue', newValue)
  
  // 恢复光标位置
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start + syntax.length, start + syntax.length)
  })
}

// 用指定语法包裹选中文本
const wrapSelection = (before: string, after: string) => {
  const textarea = textareaRef.value.$el.querySelector('textarea')
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const text = localValue.value || ''
  const selection = text.substring(start, end)
  
  const newValue = text.substring(0, start) + before + selection + after + text.substring(end)
  emit('update:modelValue', newValue)
  
  // 恢复选择范围
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start + before.length, end + before.length)
  })
}
</script>

<style lang="scss" scoped>
.markdown-editor-wrapper {
  width: 100%;
}

.editor-toolbar {
  padding: 8px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  display: flex;
  gap: 8px;
  align-items: center;
}

.markdown-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 0 0 4px 4px;
  background-color: var(--el-bg-color);
  height: 250px;
  width: 100%;

  .editor-container {
    height: 100%;
    
    :deep(.el-textarea) {
      height: 100%;
      
      .el-textarea__inner {
        height: 100%;
        font-family: monospace;
        line-height: 1.6;
        resize: none;
        border: none;
        border-radius: 0;
        padding: 16px;
      }
    }
  }

  .preview-container {
    height: 100%;
    padding: 16px;
    overflow-y: auto;

    .markdown-content {
      font-family: var(--el-font-family);
      line-height: 1.6;
      color: var(--el-text-color-primary);

      h1, h2, h3, h4, h5, h6 {
        margin-top: 24px;
        margin-bottom: 16px;
        font-weight: 600;
        line-height: 1.25;
      }

      p {
        margin-bottom: 16px;
        line-height: 1.5;
      }

      ul, ol {
        padding-left: 2em;
        margin-bottom: 16px;
      }

      code {
        padding: 0.2em 0.4em;
        margin: 0;
        font-size: 85%;
        background-color: rgba(27,31,35,0.05);
        border-radius: 3px;
      }

      pre {
        padding: 16px;
        overflow: auto;
        font-size: 85%;
        line-height: 1.45;
        background-color: #f6f8fa;
        border-radius: 3px;
      }
    }
  }
}

.description-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 16px;
  
  &.inline-mode {
    flex-direction: row;
    align-items: center;
    gap: 8px;

    .ai-reasoning-collapse {
      flex: 1;
      max-width: 200px;
      margin-bottom: 0;
      margin: 0;

      :deep(.el-collapse-item__header) {
        padding: 8px;
        background-color: transparent;
        border: none;
        color: var(--el-text-color-regular);
        font-size: 13px;
        
        &:hover {
          color: var(--el-color-primary);
        }
        
        .collapse-title {
          padding: 0;
          
          .info-icon {
            font-size: 16px;
            color: var(--el-color-info);
          }
        }
      }
    }
  }

  .ai-button {
    display: inline-flex;
    align-items: center;
    height: 40px;
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

.ai-reasoning-collapse {
  width: 100%;
  margin: 16px 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;

  :deep(.el-collapse-item__header) {
    font-size: 14px;
    color: #409eff;
    background-color: #f5f7fa;
    padding: 8px 15px;
    height: 40px;
    line-height: 24px;
    
    .collapse-title {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 8px;
    }
  }
}

.ai-reasoning-content {
  padding: 12px 15px;
  background-color: #fafafa;
  max-height: 200px;
  overflow-y: auto;

  .reasoning-text {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: var(--el-fill-color-lighter);
    border-radius: 3px;
  }
}
</style> 
