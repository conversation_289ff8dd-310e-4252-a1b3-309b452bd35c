import request from '@/config/axios'

// 评估模板项 VO
export interface TemplateItemVO {
  id: number // 项ID
  name: string // 项名称
  templateId: number // 模板ID
  templateName: string // 模板名称
  itemSchema: string // 项JSON
}

// 评估模板项 API
export const TemplateItemApi = {
  // 查询评估模板项分页
  getTemplateItemPage: async (params: any) => {
    return await request.get({ url: `/evaluation/template-item/page`, params })
  },

  // 查询评估模板项详情
  getTemplateItem: async (id: number) => {
    return await request.get({ url: `/evaluation/template-item/get?id=` + id })
  },

  // 新增评估模板项
  createTemplateItem: async (data: TemplateItemVO) => {
    return await request.post({ url: `/evaluation/template-item/create`, data })
  },

  // 修改评估模板项
  updateTemplateItem: async (data: TemplateItemVO) => {
    return await request.put({ url: `/evaluation/template-item/update`, data })
  },

  // 删除评估模板项
  deleteTemplateItem: async (id: number) => {
    return await request.delete({ url: `/evaluation/template-item/delete?id=` + id })
  },

  // 导出评估模板项 Excel
  exportTemplateItem: async (params) => {
    return await request.download({ url: `/evaluation/template-item/export-excel`, params })
  },

  // 根据模板ID删除评估模板项
  deleteTemplateItemByTemplateId: async (templateId: number) => {
    return await request.delete({
      url: `/evaluation/template-item/delete-by-template-id?templateId=` + templateId
    })
  }
}
