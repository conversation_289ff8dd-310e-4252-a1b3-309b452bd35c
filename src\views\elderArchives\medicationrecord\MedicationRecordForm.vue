<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="老人" prop="elderId">
        <el-select
          v-model="formData.elderId"
          placeholder="请选择老人"
          clearable
          filterable
          class="!w-full"
        >
          <el-option
            v-for="item in elderProfiles"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="记录日期" prop="recordDate">
        <el-date-picker
          v-model="formData.recordDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择记录日期"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="记录人员" prop="recorderName">
        <el-input v-model="formData.recorderName" placeholder="请输入记录人员" class="!w-full" />
      </el-form-item>
      <el-form-item label="用药类型" prop="isLongTerm">
        <el-radio-group v-model="formData.isLongTerm">
          <el-radio :label="true">长期用药</el-radio>
          <el-radio :label="false">临时用药</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate" v-if="formData.isLongTerm">
        <el-date-picker
          v-model="formData.startDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择开始日期"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate" v-if="formData.isLongTerm">
        <el-date-picker
          v-model="formData.endDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择结束日期"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          placeholder="请输入备注"
          type="textarea"
          :rows="3"
          class="!w-full"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { MedicationRecordApi, MedicationRecordVO } from '@/api/elderArchives/medicationrecord'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'

/** 老人用药记录 表单 */
defineOptions({ name: 'MedicationRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([]) // 老人列表
const formData = ref({
  id: undefined,
  elderId: undefined,
  recordDate: undefined,
  recorderName: undefined,
  isLongTerm: false,
  startDate: undefined,
  endDate: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '请选择老人', trigger: 'change' }],
  recordDate: [{ required: true, message: '记录日期不能为空', trigger: 'change' }],
  isLongTerm: [{ required: true, message: '请选择用药类型', trigger: 'change' }],
  startDate: [{ required: true, message: '开始日期不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 获取老人列表
  await getElderProfiles()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MedicationRecordApi.getMedicationRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 如果是长期用药，必须有开始日期
  if (formData.value.isLongTerm && !formData.value.startDate) {
    message.error('长期用药必须设置开始日期')
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MedicationRecordVO
    if (formType.value === 'create') {
      await MedicationRecordApi.createMedicationRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await MedicationRecordApi.updateMedicationRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    recordDate: undefined,
    recorderName: undefined,
    isLongTerm: false,
    startDate: undefined,
    endDate: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>