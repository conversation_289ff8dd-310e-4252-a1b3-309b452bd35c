<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用药记录ID" prop="medicationId">
        <el-input v-model="formData.medicationId" placeholder="请输入用药记录ID" />
      </el-form-item>
      <el-form-item label="药物名称" prop="medicineName">
        <el-input v-model="formData.medicineName" placeholder="请输入药物名称" />
      </el-form-item>
      <el-form-item label="服用方法" prop="usageMethod">
        <el-input v-model="formData.usageMethod" placeholder="请输入服用方法" />
      </el-form-item>
      <el-form-item label="用药剂量" prop="dosage">
        <el-input v-model="formData.dosage" placeholder="请输入用药剂量" />
      </el-form-item>
      <el-form-item label="用药频率" prop="frequency">
        <el-input v-model="formData.frequency" placeholder="请输入用药频率" />
      </el-form-item>
      <el-form-item label="用药注意事项" prop="notes">
        <el-input v-model="formData.notes" placeholder="请输入用药注意事项" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { MedicationDetailApi, MedicationDetailVO } from '@/api/elderArchives/medicationdetail'

/** 用药详情 表单 */
defineOptions({ name: 'MedicationDetailForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  medicationId: undefined,
  medicineName: undefined,
  usageMethod: undefined,
  dosage: undefined,
  frequency: undefined,
  notes: undefined,
})
const formRules = reactive({
  medicationId: [{ required: true, message: '用药记录ID不能为空', trigger: 'blur' }],
  medicineName: [{ required: true, message: '药物名称不能为空', trigger: 'blur' }],
  usageMethod: [{ required: true, message: '服用方法不能为空', trigger: 'blur' }],
  dosage: [{ required: true, message: '用药剂量不能为空', trigger: 'blur' }],
  frequency: [{ required: true, message: '用药频率不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MedicationDetailApi.getMedicationDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MedicationDetailVO
    if (formType.value === 'create') {
      await MedicationDetailApi.createMedicationDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await MedicationDetailApi.updateMedicationDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    medicationId: undefined,
    medicineName: undefined,
    usageMethod: undefined,
    dosage: undefined,
    frequency: undefined,
    notes: undefined,
  }
  formRef.value?.resetFields()
}
</script>