<template>
  <Dialog v-model="dialogVisible" title="用药记录详情" width="800px" v-loading="detailLoading">
    <el-tabs type="border-card">
      <el-tab-pane label="基本信息">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="老人姓名">
            <el-text type="primary">{{ detailData.elderName || '未知' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="记录日期">{{ formatDate(detailData.recordDate) }}</el-descriptions-item>
          <el-descriptions-item label="记录人员">{{ detailData.recorderName }}</el-descriptions-item>
          <el-descriptions-item label="用药类型">
            <el-tag :type="detailData.isLongTerm ? 'success' : 'info'">
              {{ detailData.isLongTerm ? '长期用药' : '临时用药' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始日期" v-if="detailData.isLongTerm">
            {{ formatDate(detailData.startDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束日期" v-if="detailData.isLongTerm">
            {{ formatDate(detailData.endDate) || '长期' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDate(detailData.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>

      <el-tab-pane label="用药详情">
        <div v-if="detailData.medicationDetails && detailData.medicationDetails.length > 0">
          <el-table :data="detailData.medicationDetails" border stripe>
            <el-table-column label="药物名称" prop="medicineName" min-width="120" />
            <el-table-column label="服用方法" prop="usageMethod" min-width="100">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.USAGE_METHOD" :value="scope.row.usageMethod" />
              </template>
            </el-table-column>
            <el-table-column label="用药剂量" prop="dosage" min-width="100" />
            <el-table-column label="用药频率" prop="frequency" min-width="100">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.MEDICATION_FREQUENCY" :value="scope.row.frequency" />
              </template>
            </el-table-column>
            <el-table-column label="用药注意事项" prop="notes" min-width="150" show-overflow-tooltip />
          </el-table>
        </div>
        <el-empty v-else description="暂无用药详情数据" />
      </el-tab-pane>
    </el-tabs>
  </Dialog>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { MedicationRecordApi, MedicationRecordVO } from '@/api/elderArchives/medicationrecord'
import { DICT_TYPE } from '@/utils/dict'

/** 老人用药记录详情 */
defineOptions({ name: 'MedicationRecordDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<MedicationRecordVO>({} as MedicationRecordVO) // 详情数据

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = await MedicationRecordApi.getMedicationRecord(id)
  } catch (error) {
    console.error('获取用药记录详情失败', error)
  } finally {
    detailLoading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法
</script>

<style scoped>
.el-descriptions {
  margin-bottom: 20px;
}
</style>
