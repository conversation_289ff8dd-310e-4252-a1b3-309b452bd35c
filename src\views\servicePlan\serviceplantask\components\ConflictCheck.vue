<template>
  <div class="conflict-check mt-4">
    <h4 class="text-base font-medium mb-2">冲突检测</h4>
    <div class="flex flex-col gap-2 conflict-check-content">
      <div class="conflict-check-item">
        <el-button 
          type="warning" 
          :disabled="!canCheckElderConflict"
          @click="checkElderConflict"
        >
          <Icon icon="ep:warning" class="mr-5px" />
          检测老人时间冲突
        </el-button>
        <div v-if="elderConflicts.length" class="conflict-details">
          <div v-for="(conflict, index) in elderConflicts" 
               :key="index" class="conflict-item"
          >
            <p class="conflict-text">{{ formatConflictText(conflict) }}</p>
          </div>
        </div>
      </div>
      
      <div class="conflict-check-item">
        <el-button 
          type="warning" 
          class="conflict-check-btn"
          :disabled="!canCheckExecutorConflict"
          @click="checkExecutorConflict"
        >
          <Icon icon="ep:warning" class="mr-5px" />
          检测执行人时间冲突
        </el-button>
        <div v-if="executorConflicts.length" class="conflict-details">
          <div v-for="(conflict, index) in executorConflicts" :key="index"
               class="conflict-item"
          >
            <p class="conflict-text">{{ formatConflictText(conflict) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { PlanExecutorApi } from '@/api/servicePlan/planexecutor'
import dayjs from 'dayjs'

// 定义 props
const props = defineProps({
  generatedDates: {
    type: Array,
    required: true
  },
  form: {
    type: Object,
    required: true
  },
  selectedExecutors: {
    type: Array,
    required: true
  },
  userList: {
    type: Array,
    required: true
  },
  planInfo: {
    type: Object,
    required: true
  }
})

// 冲突状态
const elderConflicts = ref<any[]>([])
const executorConflicts = ref<any[]>([])

// 检测老人时间冲突
const checkElderConflict = async () => {
  try {
    const response = await PlanExecutorApi.checkElderConflict({
      elderId: props.planInfo.elderId,
      dates: props.generatedDates,
      startTime: props.form.startTime!,
      endTime: props.form.endTime!
    })

    elderConflicts.value = response
    if (response.length > 0) {
      ElMessage.warning(`发现 ${response.length} 个老人时间冲突`)
    } else {
      ElMessage.success('未发现老人时间冲突')
    }
  } catch (error) {
    console.error('检测老人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

// 检测执行人时间冲突
const checkExecutorConflict = async () => {
  try {
    executorConflicts.value = []
    for (const executorId of props.selectedExecutors) {
      const response = await PlanExecutorApi.checkExecutorConflict({
        executorId,
        dates: props.generatedDates,
        startTime: props.form.startTime!,
        endTime: props.form.endTime!
      })

      if (response.length > 0) {
        const executor = props.userList.find(user => user.id === executorId)
        executorConflicts.value.push(...response.map(conflict => ({
          ...conflict,
          executorName: executor?.nickname || executorId
        })))
        ElMessage.warning(`执行人 ${executor?.nickname || executorId} 有 ${response.length} 个时间冲突`)
      }
    }
    
    if (executorConflicts.value.length === 0) {
      ElMessage.success('未发现执行人时间冲突')
    }
  } catch (error) {
    console.error('检测执行人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

// 格式化冲突文本
const formatConflictText = (conflict: any): string => {
  const startTime = dayjs(conflict.startTime).format('YYYY-MM-DD HH:mm')
  const endTime = dayjs(conflict.endTime).format('YYYY-MM-DD HH:mm')
  const taskInfo = `${conflict.planName || '未知计划'}`
  const personInfo = conflict.executorName 
    ? `执行人: ${conflict.executorName}` 
    : `老人: ${conflict.elderName}`
  
  return `${startTime} 至 ${endTime}\n${taskInfo} (${personInfo})`
}

// 判断是否可以检测冲突
const canCheckElderConflict = computed(() => {
  return (
    props.generatedDates.length > 0 &&
    props.form.startTime &&
    props.form.endTime
  )
})

const canCheckExecutorConflict = computed(() => {
  return (
    props.selectedExecutors.length > 0 && 
    props.generatedDates.length > 0 &&
    props.form.startTime &&
    props.form.endTime
  )
})
</script>

<style scoped>
.conflict-check {
  width: 100%;
  padding-left: 20px;
  border-top: 1px solid var(--el-border-color);
  padding-top: 16px;

  .conflict-check-content {
    max-height: 300px;
    overflow-y: auto;
  }

  .conflict-check-btn {
    width: 100%;
    margin: 0px;
  }
  
  .conflict-check-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .conflict-details {
    padding: 12px;
    background-color: var(--el-color-warning-light-9);
    border-radius: 4px;
  }

  .conflict-item {
    padding: 8px;
    background-color: var(--el-color-warning-light-8);
    border-radius: 4px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .conflict-text {
    color: var(--el-color-warning-darker);
    margin: 0;
    line-height: 1.6;
    white-space: pre-line;
    font-size: 13px;
  }
}
</style> 