<template>
  <Dialog :title="'查看老人疾病诊断详情'" v-model="dialogVisible" width="800px">
    <div class="detail-container" v-loading="detailLoading">
      <!-- 老人与诊断基本信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">老人姓名：</span>
                <span class="value">{{ detailData.elderName || '未关联' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">老人ID：</span>
                <span class="value">{{ detailData.elderId }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断日期：</span>
                <span class="value">{{ formatDate(detailData.diagnosisDate) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24" class="mt-10px">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断医生：</span>
                <span class="value">{{ detailData.doctorName || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断机构：</span>
                <span class="value">{{ detailData.hospitalName || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断类型：</span>
                <span class="value">
                  <dict-tag v-if="detailData.diagnosisType !== undefined" :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="detailData.diagnosisType" />
                  <span v-else>未知</span>
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24" class="mt-10px" v-if="detailData.remark">
            <el-col :span="24">
              <div class="info-item">
                <span class="label">备注：</span>
                <span class="value">{{ detailData.remark }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 疾病诊断详情列表卡片 -->
      <div class="detail-card mt-20px" v-if="diagnosisDetails.length > 0">
        <div class="card-header">
          <el-icon><FirstAidKit /></el-icon>
          <span>疾病诊断详情</span>
        </div>
        <div class="card-content">
          <el-table :data="diagnosisDetails" border stripe>
            <el-table-column label="疾病编码" prop="diseaseCode" align="center" />
            <el-table-column label="疾病名称" prop="diseaseName" align="center" />
            <el-table-column label="是否主要诊断" align="center">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.isMain ? 'true' : 'false'" />
              </template>
            </el-table-column>
            <el-table-column label="诊断描述" prop="description" align="center" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
      <div class="detail-card mt-20px" v-else>
        <div class="card-header">
          <el-icon><FirstAidKit /></el-icon>
          <span>疾病诊断详情</span>
        </div>
        <div class="card-content">
          <el-empty description="暂无疾病诊断详情数据" />
        </div>
      </div>

      <!-- 系统信息卡片 -->
      <div class="detail-card mt-20px">
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>系统信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(detailData.createTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">创建人：</span>
                <span class="value">{{ detailData.creator || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">更新时间：</span>
                <span class="value">{{ formatDate(detailData.updateTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { DiseaseDiagnosisApi, DiseaseDiagnosisVO } from '@/api/elderArchives/diseasediagnosis'
import { DICT_TYPE } from '@/utils/dict'
import { DiseaseDiagnosisDetailApi, DiseaseDiagnosisDetailVO } from '@/api/elderArchives/diseasediagnosisdetail'

/** 老人疾病诊断详情 */
defineOptions({ name: 'DiseaseDiagnosisDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<Partial<DiseaseDiagnosisVO & {
  elderName?: string
}>>({}) // 详情数据
const diagnosisDetails = ref<DiseaseDiagnosisDetailVO[]>([]) // 疾病诊断详情列表

/** 打开弹窗 */
const open = async (row: DiseaseDiagnosisVO) => {
  dialogVisible.value = true
  // 设置数据
  if (row.id) {
    detailLoading.value = true
    try {
      // 获取疾病诊断详情
      const detail = await DiseaseDiagnosisApi.getDiseaseDiagnosisWithRelated(row.id)
      detailData.value = detail
      
      // 获取关联的疾病诊断详情列表
      const params = {
        pageNo: 1,
        pageSize: 100,
        diagnosisId: row.id
      }
      const detailsResult = await DiseaseDiagnosisDetailApi.getDiseaseDiagnosisDetailPage(params)
      diagnosisDetails.value = detailsResult.list || []
    } finally {
      detailLoading.value = false
    }
  }
}

// 暴露方法
defineExpose({ open })
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 10px;
}

.detail-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  background-color: #f5f7fa;
  padding: 12px 15px;
  display: flex;
  align-items: center;
  font-weight: bold;
  color: #303133;
  border-bottom: 1px solid #ebeef5;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.card-content {
  padding: 15px;
  background-color: #fff;
}

.info-item {
  display: flex;
  align-items: flex-start;
  
  .label {
    color: #606266;
    font-weight: bold;
    min-width: 90px;
  }
  
  .value {
    color: #303133;
    word-break: break-all;
  }
}
</style>
