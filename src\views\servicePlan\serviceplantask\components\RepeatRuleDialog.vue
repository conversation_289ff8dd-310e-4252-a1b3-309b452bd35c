<template>
  <el-dialog
    title="设置重复规则"
    v-model="dialogVisible"
    width="500px"
  >
    <el-form label-width="100px">
      <el-form-item label="重复类型">
        <el-radio-group 
          v-model="repeatRule.type"
          @change="handleTypeChange"
        >
          <el-radio label="daily">每天</el-radio>
          <el-radio label="weekly">每周</el-radio>
          <el-radio label="monthly">每月</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        v-if="repeatRule.type === 'daily'"
        label="重复间隔"
      >
        <el-input-number 
          v-model="repeatRule.interval" 
          :min="1" 
          :max="30"
          controls-position="right"
        />
        <span class="ml-2">天</span>
      </el-form-item>

      <el-form-item 
        v-if="repeatRule.type === 'weekly'"
        label="重复星期"
      >
        <div class="week-days-wrapper">
          <el-checkbox-group v-model="repeatRule.days" class="week-days">
            <el-checkbox 
              v-for="day in weekDays" 
              :key="day.value" 
              :label="day.value"
              class="week-day-item"
            >
              {{ day.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>

      <el-form-item 
        v-if="repeatRule.type === 'monthly'"
        label="重复日期"
      >
        <div class="month-days-wrapper">
          <el-checkbox-group v-model="repeatRule.days" class="month-days">
            <el-checkbox 
              v-for="day in monthDays" 
              :key="day" 
              :label="day"
              class="month-day-item"
            >
              {{ day }}日
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { ServicePlanVO } from '@/api/servicePlan/serviceplan'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'RepeatRuleDialog'
})

const props = defineProps<{
  visible: boolean
  selectedPlan?: ServicePlanVO
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [pattern: string]
}>()

interface RepeatRuleState {
  type: 'daily' | 'weekly' | 'monthly'
  interval: number
  days: number[]
}

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 重复规则表单
const repeatRule = reactive<RepeatRuleState>({
  type: 'daily',
  interval: 1,
  days: []
})

// 星期选项
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 月份日期选项
const monthDays = Array.from({ length: 31 }, (_, i) => i + 1)

// 处理重复类型变化
const handleTypeChange = () => {
  repeatRule.days = []
  repeatRule.interval = 1
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  repeatRule.type = 'daily'
  repeatRule.interval = 1
  repeatRule.days = []
}

// 确认
const handleConfirm = () => {
  if (!props.selectedPlan) {
    ElMessage.warning('请先选择护理计划')
    return
  }

  const pattern: any = {
    type: repeatRule.type,
    interval: repeatRule.type === 'daily' ? repeatRule.interval : undefined,
    startDate: dayjs(props.selectedPlan.startDate).format('YYYY-MM-DD'),
    endDate: dayjs(props.selectedPlan.endDate).format('YYYY-MM-DD')
  }
  
  if (repeatRule.type === 'weekly' && repeatRule.days.length > 0) {
    pattern.days = repeatRule.days
  } else if (repeatRule.type === 'monthly' && repeatRule.days.length > 0) {
    pattern.days = repeatRule.days
  }
  
  emit('confirm', JSON.stringify(pattern))
  handleClose()
}
</script>

<style lang="scss" scoped>
.el-checkbox-group {
  display: block;
}

.el-checkbox {
  margin-right: 0;
  width: auto;
}

.ml-2 {
  margin-left: 8px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-radio-group) {
  display: flex;
  gap: 16px;
}

:deep(.el-input-number) {
  width: 120px;
}

.week-days-wrapper,
.month-days-wrapper {
  .el-checkbox-group {
    display: grid;
    grid-template-columns: repeat(7, 1fr); // 每行7个
    gap: 8px;
    padding: 4px;
  }

  .week-day-item,
  .month-day-item {
    margin-right: 0;
    text-align: center;
    
    :deep(.el-checkbox__label) {
      padding-left: 6px;
      font-size: 13px;
    }

    :deep(.el-checkbox__input) {
      margin-right: 2px;
    }
  }
}

.month-days-wrapper,
.week-days-wrapper {
  .el-checkbox-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
  }
}
</style> 