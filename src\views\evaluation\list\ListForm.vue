<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
        <!-- <Editor v-model="formData.description" height="300px" /> -->
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="typeValue"
          multiple
          :multiple-limit="1"
          filterable
          allow-create
          default-first-option
          :reserve-keyword="false"
          placeholder="请选择类型"
          @change="handleTypeChange"
          ref="typeSelect"
        >
          <template #prefix>
            <el-tooltip content="仅支持选择单个选项，且支持创建新的选项" placement="top">
              <el-icon>
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_LIST_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评估模板" prop="selectedTemplates">
        <el-select
          v-model="selectedTemplates"
          multiple
          filterable
          placeholder="请选择评估模板"
          style="width: 100%"
          @change="handleTemplateChange"
        >
          <el-option
            v-for="item in templateOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <div class="text-xs text-gray-400 mt-1"> 已选择 {{ selectedTemplates.length }} 个模板 </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ListApi, ListVO } from '@/api/evaluation/list'
import { TemplateApi } from '@/api/evaluation/template'
import { InfoFilled } from '@element-plus/icons-vue'

defineOptions({ name: 'ListForm' })

// 国际化
const { t } = useI18n()
// 消息弹窗
const message = useMessage()

// 弹窗的是否展示
const dialogVisible = ref(false)
// 弹窗的标题
const dialogTitle = ref('')
// 表单的加载
const formLoading = ref(false)
// 表单的类型
const formType = ref('')
// 表单的数据
const formData = ref({
  id: undefined,
  name: undefined,
  description: undefined,
  type: '',
  status: undefined,
  templateIds: ''
})

// 类型相关变量
const typeValue = ref<string[]>([])
// 类型选择器
const typeSelect = ref()
// 类型选项
const typeOptions = [
  {
    value: '入住评估',
    label: '入住评估'
  },
  {
    value: '综合评估',
    label: '综合评估'
  },
  {
    value: '专项评估',
    label: '专项评估'
  }
]

// 表单的规则
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
})
// 表单的 Ref
const formRef = ref()

// 添加选中的模板ID数组和模板选项列表
const selectedTemplates = ref<number[]>([])
// 定义模板选项的接口，与getSimpleTemplateList返回的结构匹配
interface TemplateOption {
  id: number
  name: string
}
// 模板选项列表
const templateOptions = ref<TemplateOption[]>([])

// 获取模板列表
const getTemplateOptions = async () => {
  try {
    // 使用模板API获取所有可用模板
    const res = await TemplateApi.getSimpleTemplateList()
    templateOptions.value = res || []
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  }
}

// 处理模板选择变化
const handleTemplateChange = () => {
  // 将选中的模板ID数组转换为逗号分隔的字符串
  if (selectedTemplates.value.length > 0) {
    formData.value.templateIds = selectedTemplates.value.join(',')
  } else {
    formData.value.templateIds = ''
  }
}

// 处理类型变化
const handleTypeChange = () => {
  // 如果选择了一个选项，就关闭下拉框
  if (typeValue.value.length === 1) {
    typeSelect.value?.blur()
    formData.value.type = typeValue.value[0]
  } else {
    formData.value.type = ''
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 获取模板选项
  await getTemplateOptions()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ListApi.getList(id)
      // 设置类型选中值
      if (formData.value.type) {
        typeValue.value = [formData.value.type]
      }
      // 如果有模板IDs，转换为数组并设置选中状态
      if (formData.value.templateIds && typeof formData.value.templateIds === 'string') {
        selectedTemplates.value = formData.value.templateIds
          .split(',')
          .filter((id) => id.trim() !== '')
          .map((id) => parseInt(id))
      }
    } finally {
      formLoading.value = false
    }
  }
}

// 暴露 open 方法，用于打开弹窗
defineExpose({ open })

// 定义 success 事件
const emit = defineEmits(['success'])

/** 提交表单 */
const submitForm = async () => {
  // 确保类型已正确设置
  if (typeValue.value.length === 1) {
    formData.value.type = typeValue.value[0]
  }

  // 校验表单
  await formRef.value.validate()

  // 确保模板ID已转换为逗号分隔的字符串
  formData.value.templateIds = selectedTemplates.value.join(',')

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ListVO
    if (formType.value === 'create') {
      await ListApi.createList(data)
      message.success(t('common.createSuccess'))
    } else {
      await ListApi.updateList(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    description: undefined,
    type: '',
    status: undefined,
    templateIds: ''
  }
  typeValue.value = [] // 重置类型选择
  selectedTemplates.value = [] // 重置选中的模板
  formRef.value?.resetFields()
}
</script>
