<template>
  <el-form
    ref="formRef"
    :model="props.form"
    :rules="props.rules"
    label-width="100px"
    v-loading="props.loading"
    class="form-container mr-4"
  >
    <!-- 基础信息部分 -->
    <el-form-item label="任务名称" prop="name">
      <el-input
        :model-value="props.form.name"
        @update:model-value="updateFormField('name', $event)"
        placeholder="请输入任务名称"
        clearable
        style="width: 360px;"
      />
    </el-form-item>
    <el-form-item label="任务分类" prop="categoryId">
      <el-tree-select
        :model-value="props.form.categoryId"
        @update:model-value="(val) => { updateFormField('categoryId', val); handleCategoryChange(val); }"
        :data="props.categoryTree"
        :props="props.categoryProps"
        placeholder="请选择任务分类"
        clearable
        style="width: 360px;"
      />
    </el-form-item>
    <el-form-item label="是否周期任务" prop="isRecurring">
      <el-radio-group 
        :model-value="props.form.isRecurring"
        @update:model-value="(val) => { updateFormField('isRecurring', val); handleRecurringChange(val); }"
      >
        <el-radio
          v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING)"
          :key="dict.value"
          :label="dict.value"
        >
          {{ dict.label }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 非周期任务时显示日期选择 -->
    <el-form-item 
      v-if="props.form.isRecurring === 0" 
      label="执行日期" 
      prop="executeDate"
    >
      <el-date-picker
        :model-value="props.form.executeDate"
        @update:model-value="updateFormField('executeDate', $event)"
        type="date"
        value-format="YYYY-MM-DD"
        placeholder="选择执行日期"
        class="date-input"
        style="width: 360px;"
      />
    </el-form-item>

    <!-- 周期任务时显示重复规则 -->
    <el-form-item 
      v-else-if="props.form.isRecurring === 1"
      label="周期性规则" 
      prop="repeatPattern"
    >
      <el-button 
        type="primary" 
        plain
        @click="openRepeatRuleDialog"
      >
        自定义重复规则
      </el-button>
    </el-form-item>

    <el-form-item label="时间段" required>
      <div class="time-range-wrapper">
        <el-time-picker
          :model-value="props.form.startTime"
          @update:model-value="updateFormField('startTime', $event)"
          value-format="HH:mm"
          format="HH:mm"
          :controls="false"
          placeholder="开始时间"
          clearable
        />
        <span class="time-separator">至</span>
        <el-time-picker
          :model-value="props.form.endTime"
          @update:model-value="updateFormField('endTime', $event)"
          value-format="HH:mm"
          format="HH:mm"
          :controls="false"
          placeholder="结束时间"
          clearable
          :min-time="props.form.startTime"
        />
      </div>
    </el-form-item>

    <el-form-item label="优先级" prop="priority">
      <el-select 
        :model-value="props.form.priority"
        @update:model-value="updateFormField('priority', $event)"
        placeholder="请选择优先级"
        class="priority-select"
      >
        <el-option
          v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY)"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="执行人" prop="recommendedExecutors">
      <div class="executor-select-wrapper">
        <el-select
          :model-value="props.selectedExecutors"
          @update:model-value="handleExecutorsChange"
          multiple
          placeholder="请选择执行人"
          filterable
          class="form-input"
        >
          <el-option
            v-for="user in props.userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          >
            <span>{{ user.nickname }}</span>
          </el-option>
        </el-select>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ElMessage } from 'element-plus'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { PlanExecutorApi } from '@/api/servicePlan/planexecutor'
import RepeatRuleDialog from './RepeatRuleDialog.vue'

// 定义 TaskFormData 接口
interface TaskFormData {
  planId?: number 
  elderId?: number
  name?: string
  taskType?: string
  status?: number
  isRecurring?: number
  repeatPattern?: string
  executeDate?: string
  startTime?: string
  endTime?: string
  recommendedExecutors?: string
  priority?: number
  description?: string
  categoryId?: number
}

// 定义 props
const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  categoryTree: {
    type: Array,
    default: () => []
  },
  categoryProps: {
    type: Object,
    required: true
  },
  userList: {
    type: Array,
    default: () => []
  },
  selectedExecutors: {
    type: Array,
    default: () => []
  }
})

// 定义事件
const emit = defineEmits([
  'handleExecutorsChange',
  'handleRecurringChange',
  'openRepeatRuleDialog',
  'handleCategoryChange',
  'updateCategoryTree',
  'update:form'
])

// 更新表单字段的方法
const updateFormField = (field, value) => {
  const updatedForm = { ...props.form, [field]: value }
  emit('update:form', updatedForm)
}

// 处理执行人变化
const handleExecutorsChange = (value: number[]) => {
  emit('handleExecutorsChange', value)
  
  // 同时更新表单中的 recommendedExecutors 字段
  const jsonValue = JSON.stringify(value)
  updateFormField('recommendedExecutors', jsonValue)
}

// 处理周期任务选择变化
const handleRecurringChange = (value: number) => {
  emit('handleRecurringChange', value)
}

// 打开重复规则弹窗
const openRepeatRuleDialog = () => {
  emit('openRepeatRuleDialog')
}

// 处理分类变化
const handleCategoryChange = (categoryId: number | null) => {
  emit('handleCategoryChange', categoryId)
}

// 获取分类树数据
const getCategoryTree = async () => {
  try {
    const treeData = await TaskCategoryApi.generateSimpleTree()
    emit('updateCategoryTree', treeData || [])
  } catch (error) {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  }
}

// 初始化数据
onMounted(() => {
  // 这里不再需要调用 getUserList 和 getCategoryTree
  // 因为这些数据现在由父组件提供
})
</script>

<style scoped>
.form-container {
  width: 55%;
  padding-right: 20px;
}

.form-input {
  width: 360px !important;
}

.priority-select {
  width: 360px !important;
}

.time-range-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 360px;

  :deep(.el-time-picker) {
    flex: 1;
  }

  .time-separator {
    flex: none;
  }
}

.executor-select-wrapper {
  :deep(.el-select) {
    width: 360px !important;
  }
}
</style> 