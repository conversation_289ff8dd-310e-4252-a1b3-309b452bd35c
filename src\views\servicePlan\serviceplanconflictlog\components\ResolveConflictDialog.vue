<template>
  <Dialog :title="'解决时间冲突'" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="服务计划" prop="planId">
        <el-input v-model="formData.planName" disabled />
      </el-form-item>
      <el-form-item label="任务名称" prop="taskId">
        <el-input v-model="formData.taskName" disabled />
      </el-form-item>
      <el-form-item label="类型任务" prop="taskId">
        <el-input v-model="formData.taskType" disabled />
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-input 
          v-model="formData.elderName" 
          disabled
        />
      </el-form-item>
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="formData.executorId"
          placeholder="请选择执行人"
          filterable
          clearable
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          >
            <span>{{ user.nickname }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="执行时间" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          value-format="x"
          format="YYYY-MM-DD HH:mm"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          class="!w-[360px]"
          popper-class="conflict-dialog-date-picker"
          @change="handleTimeRangeChange"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex items-center justify-between">
        <div>
          <el-button
            type="warning"
            :disabled="!canCheckElderConflict"
            @click="checkElderConflict"
          >
            检测老人冲突
          </el-button>
          <el-button
            type="warning"
            :disabled="!canCheckExecutorConflict"
            @click="checkExecutorConflict"
          >
            检测执行人冲突
          </el-button>
        </div>
        <div>
          <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { PlanExecutorApi, PlanExecutorVO } from '@/api/servicePlan/planexecutor'
import { ConflictLogApi } from '@/api/servicePlan/serviceplanconflictlog'
import { getSimpleUserList } from '@/api/system/user'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({ name: 'ResolveConflictDialog' })

const message = useMessage()
const dialogVisible = ref(false)
const formLoading = ref(false)

interface FormData extends Partial<PlanExecutorVO> {
  startTime?: number
  endTime?: number
  elderId?: number
  planName?: string
  elderName?: string
  taskType?: string
  taskName?: string
}

const formData = ref<FormData>({
  id: undefined,
  taskId: undefined,
  executorId: undefined,
  elderId: undefined,
  planId: undefined,
  startTime: undefined,
  endTime: undefined,
  actualStart: undefined,
  actualEnd: undefined
})

const formRules = reactive({
  executorId: [{ required: true, message: '执行人ID不能为空', trigger: 'blur' }],
})

const formRef = ref()
const emit = defineEmits(['success'])

// 用户列表
const userList = ref<{id: number, nickname: string}[]>([])

// 获取用户列表
const getUserList = async () => {
  try {
    const data = await getSimpleUserList()
    userList.value = data
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 老人列表
const elderList = ref([])

// 获取老人列表
const getElderList = async () => {
  try {
    const data = await getSimpleUserList() // 暂时使用用户列表API
    elderList.value = data
  } catch (error) {
    console.error('获取老人列表失败:', error)
  }
}

// 在 onMounted 中调用
onMounted(() => {
  getUserList()
  getElderList()
})

// 时间范围
const timeRange = ref<[number, number] | null>(null)

// 处理时间范围变化
const handleTimeRangeChange = (val: [number, number] | null) => {
  if (val) {
    formData.value.startTime = val[0]
    formData.value.endTime = val[1]
  } else {
    formData.value.startTime = undefined
    formData.value.endTime = undefined
  }
}

/** 打开弹窗 */
const open = async (taskExecutorId: number) => {
  dialogVisible.value = true
  resetForm()
  
  if (taskExecutorId) {
    formLoading.value = true
    try {
      const data = await PlanExecutorApi.getFlictPlanExecutor(taskExecutorId)
      formData.value = {
        ...data,
        startTime: data.startTime,
        endTime: data.endTime
      }
      // 设置时间范围
      timeRange.value = [data.startTime, data.endTime]
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open })

// 冲突状态
const elderConflicts = ref<any[]>([])
const executorConflicts = ref<any[]>([])

// 判断是否可以检测冲突
const canCheckElderConflict = computed(() => {
  return formData.value.elderId && 
         formData.value.startTime &&
         formData.value.endTime
})

const canCheckExecutorConflict = computed(() => {
  return formData.value.executorId && 
         formData.value.startTime &&
         formData.value.endTime
})

// 检测老人时间冲突
const checkElderConflict = async () => {
  try {
    const response = await PlanExecutorApi.checkElderTimeConflict({
      taskId: formData.value.taskId!,
      elderId: formData.value.elderId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    elderConflicts.value = response
    if (response.length > 0) {
      ElMessage.warning(`发现 ${response.length} 个老人时间冲突`)
    } else {
      ElMessage.success('未发现老人时间冲突')
    }
  } catch (error) {
    console.error('检测老人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

// 检测执行人时间冲突
const checkExecutorConflict = async () => {
  try {
    const response = await PlanExecutorApi.checkExecutorTimeConflict({
      id: formData.value.id,
      executorId: formData.value.executorId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    executorConflicts.value = response
    if (response.length > 0) {
      ElMessage.warning(`发现 ${response.length} 个执行人时间冲突`)
    } else {
      ElMessage.success('未发现执行人时间冲突')
    }
  } catch (error) {
    console.error('检测执行人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验时间范围
  if (!formData.value.startTime || !formData.value.endTime) {
    message.error('请选择执行时间')
    return
  }
  
  await formRef.value.validate()
  
  // 先检测冲突
  const canContinue = await checkAllConflicts()
  if (!canContinue) {
    return
  }

  formLoading.value = true
  try {
    const submitData: PlanExecutorVO = {
      ...formData.value,
      startTime: formData.value.startTime,
      endTime: formData.value.endTime
    }
    await PlanExecutorApi.updatePlanExecutor(submitData)
    
    // 更新冲突状态
    await ConflictLogApi.updateConflictLogStatus({
      taskExecutorId: formData.value.id!,
      resolveStatus: 2, // 已解决状态
      resolveComment: '时间冲突已解决'
    })
    
    message.success('修改成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskId: undefined,
    executorId: undefined,
    elderId: undefined,
    planId: undefined,
    startTime: undefined,
    endTime: undefined,
    actualStart: undefined,
    actualEnd: undefined,
    planName: undefined,
    elderName: undefined,
    taskType: undefined,
    taskName: undefined
  }
  timeRange.value = null
  formRef.value?.resetFields()
  elderConflicts.value = []
  executorConflicts.value = []
}

// 检查所有冲突
const checkAllConflicts = async () => {
  if (!canCheckElderConflict.value || !canCheckExecutorConflict.value) {
    ElMessage.warning('请填写完整的执行时间和相关ID')
    return false
  }

  try {
    // 检查老人冲突
    const elderResponse = await PlanExecutorApi.checkElderTimeConflict({
      taskId: formData.value.taskId!,
      elderId: formData.value.elderId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    // 检查执行人冲突
    const executorResponse = await PlanExecutorApi.checkExecutorTimeConflict({
      id: formData.value.id,
      executorId: formData.value.executorId!,
      startTime: formData.value.startTime!,
      endTime: formData.value.endTime!
    })

    // 有冲突时提示用户
    if (elderResponse.length > 0 || executorResponse.length > 0) {
      const confirmMessage = [
        '检测到以下冲突:',
        elderResponse.length > 0 ? `- ${elderResponse.length}个老人时间冲突` : '',
        executorResponse.length > 0 ? `- ${executorResponse.length}个执行人时间冲突` : '',
        '是否仍要继续保存？'
      ].filter(Boolean).join('\n')

      try {
        await ElMessageBox.confirm(confirmMessage, '时间冲突提醒', {
          type: 'warning',
          confirmButtonText: '继续保存',
          cancelButtonText: '取消'
        })
        return true // 用户确认继续
      } catch {
        return false // 用户取消
      }
    }

    return true // 没有冲突
  } catch (error) {
    console.error('检测冲突失败:', error)
    ElMessage.error('检测冲突失败')
    return false
  }
}

// 监听开始时间变化，自动调整结束时间
watch(() => formData.value.startTime, (newStartTime) => {
  if (newStartTime && formData.value.endTime && formData.value.endTime <= newStartTime) {
    formData.value.endTime = undefined
  }
})

</script>

<style lang="scss">
/* 注意：这里没有使用 scoped，这样样式可以应用到弹出的日期选择器 */
.conflict-dialog-date-picker {
  .el-picker-panel__footer {
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    
    .el-button {
      padding: 8px 16px !important; /* 减小高度 */
      font-size: 14px !important;
      border-radius: 4px !important;
      min-width: 70px !important;
      height: 32px !important; /* 减小固定高度 */
      line-height: 16px !important;
      
      /* 清空按钮 - 改为红色样式 */
      &:first-child {
        background-color: #fef0f0 !important; /* 淡红色背景 */
        color: #f56c6c !important; /* 红色文字 */
        border: none !important;
        margin-right: 8px !important;
        
        &:hover {
          background-color: #fde2e2 !important; /* 深一点的红色背景 */
          color: #f56c6c !important; /* 保持红色文字 */
        }
      }
      
      /* 确认按钮 */
      &:last-child {
        background-color: var(--el-color-primary) !important;
        color: #fff !important;
        border: none !important;
        
        &:hover {
          background-color: var(--el-color-primary-light-3) !important;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


</style> 