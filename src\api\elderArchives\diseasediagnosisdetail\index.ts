import request from '@/config/axios'

// 疾病诊断详情 VO
export interface DiseaseDiagnosisDetailVO {
  id: number // 主键ID
  diagnosisId: number // 诊断ID
  diseaseCode: string // 疾病编码(ICD-10)
  diseaseName: string // 疾病名称
  description: string // 诊断描述
  isMain: boolean // 是否主要诊断
  createTime?: Date // 创建时间
}

// 带关联信息的疾病诊断详情 VO
export interface DiseaseDiagnosisDetailWithRelatedVO extends DiseaseDiagnosisDetailVO {
  elderName?: string // 老人姓名
  elderId?: number // 老人ID
  diagnosisDate?: string // 诊断日期
  doctorName?: string // 诊断医生
  hospitalName?: string // 诊断机构
  diagnosisType?: number // 诊断类型
}

// 疾病诊断详情 API
export const DiseaseDiagnosisDetailApi = {
  // 查询疾病诊断详情分页
  getDiseaseDiagnosisDetailPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/disease-diagnosis-detail/page`, params })
  },

  // 查询疾病诊断详情详情
  getDiseaseDiagnosisDetail: async (id: number) => {
    return await request.get({ url: `/elderArchives/disease-diagnosis-detail/get?id=` + id })
  },

  // 查询带关联信息的疾病诊断详情
  getDiseaseDiagnosisDetailWithRelated: async (id: number) => {
    return await request.get({ url: `/elderArchives/disease-diagnosis-detail/get-with-related?id=` + id })
  },

  // 新增疾病诊断详情
  createDiseaseDiagnosisDetail: async (data: DiseaseDiagnosisDetailVO) => {
    return await request.post({ url: `/elderArchives/disease-diagnosis-detail/create`, data })
  },

  // 修改疾病诊断详情
  updateDiseaseDiagnosisDetail: async (data: DiseaseDiagnosisDetailVO) => {
    return await request.put({ url: `/elderArchives/disease-diagnosis-detail/update`, data })
  },

  // 删除疾病诊断详情
  deleteDiseaseDiagnosisDetail: async (id: number) => {
    return await request.delete({ url: `/elderArchives/disease-diagnosis-detail/delete?id=` + id })
  },

  // 导出疾病诊断详情 Excel
  exportDiseaseDiagnosisDetail: async (params) => {
    return await request.download({ url: `/elderArchives/disease-diagnosis-detail/export-excel`, params })
  },
}
