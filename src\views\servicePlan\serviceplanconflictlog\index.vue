<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="queryParams.executorId"
          placeholder="请选择执行人"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          >
            <span>{{ user.nickname }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="冲突类型：" prop="conflictType">
        <el-select v-model="queryParams.conflictType" placeholder="请选择冲突类型" clearable class="!w-240px">
          <el-option label="时间冲突" value="time" />
          <el-option label="执行人冲突" value="executor" />
        </el-select>
      </el-form-item>
      <el-form-item label="解决状态：" prop="resolveStatus">
        <el-select
          v-model="queryParams.resolveStatus"
          placeholder="请选择解决状态："
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_CONFLICT_LOG_RESOLVE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="解决备注" prop="resolveComment">
        <el-input
          v-model="queryParams.resolveComment"
          placeholder="请输入解决备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          @click="openForm('create')"-->
<!--          v-hasPermi="['servicePlan:conflict-log:create']"-->
<!--        >-->
<!--          <Icon icon="ep:plus" class="mr-5px" /> 新增-->
<!--        </el-button>-->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['servicePlan:conflict-log:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="任务名称" align="center" prop="taskName" />
      <el-table-column label="任务类型" align="center" prop="taskType" />
      <el-table-column label="执行人" align="center" prop="executorName" />
      <el-table-column label="冲突类型" align="center" prop="conflictType">
        <template #default="scope">
          <el-tag :type="scope.row.conflictType === 'time' ? 'warning' : 'danger'">
            {{ scope.row.conflictType === 'time' ? '时间冲突' : '执行人冲突' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="解决状态：" align="center" prop="resolveStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_CONFLICT_LOG_RESOLVE_STATUS" :value="scope.row.resolveStatus" />
        </template>
      </el-table-column>
      <el-table-column label="解决备注" align="center" prop="resolveComment" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:conflict-log:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success"
            @click="handleResolveConflict(scope.row)"
            v-if="scope.row.resolveStatus === 1"
          >
            解决冲突
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['servicePlan:conflict-log:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ConflictLogForm ref="formRef" @success="getList" :user-list="userList" />

  <!-- 添加冲突解决弹窗 -->
  <ResolveConflictDialog ref="resolveDialogRef" @success="handleResolveSuccess" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ConflictLogApi, ConflictLogVO } from '@/api/servicePlan/serviceplanconflictlog'
import ConflictLogForm from './ConflictLogForm.vue'
import ResolveConflictDialog from './components/ResolveConflictDialog.vue'
import { getSimpleUserList } from '@/api/system/user'

/** 任务冲突日志 列表 */
defineOptions({ name: 'ServicePlanConflictLog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ConflictLogVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  executorId: undefined,
  conflictType: undefined,
  resolveStatus: undefined,
  resolveComment: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 用户列表
const userList = ref([])

// 获取用户列表
const getSimpleUsers = async () => {
  try {
    const data = await getSimpleUserList()
    userList.value = data
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ConflictLogApi.getConflictLogPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ConflictLogApi.deleteConflictLog(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ConflictLogApi.exportConflictLog(queryParams)
    download.excel(data, '任务冲突日志.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 冲突解决弹窗引用 */
const resolveDialogRef = ref()

/** 打开冲突解决弹窗 */
const handleResolveConflict = (row: any) => {
  if (!row.taskExecutorId) {
    message.warning('缺少任务执行人关联ID')
    return
  }
  resolveDialogRef.value?.open(row.taskExecutorId)
}

/** 处理冲突解决成功 */
const handleResolveSuccess = async () => {
  // 刷新列表
  await getList()
  message.success('冲突已解决')
}

/** 初始化 **/
onMounted(async () => {
  await getSimpleUsers() // 先获取用户列表
  await getList()
})
</script>
