<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="选择疾病诊断" prop="diagnosisId">
        <el-select
          v-model="formData.diagnosisId"
          placeholder="请选择疾病诊断"
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="item in diagnosisList"
            :key="item.id"
            :label="`${item.elderName} - ${item.diagnosisDate} - ${getDiagnosisTypeLabel(item.diagnosisType)}`"
            :value="item.id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center">
              <span>{{ item.elderName }}</span>
              <span>{{ item.diagnosisDate }}</span>
              <dict-tag :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="item.diagnosisType" />
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择用药记录" prop="medicationId">
        <el-select
          v-model="formData.medicationId"
          placeholder="请选择用药记录"
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="item in medicationList"
            :key="item.id"
            :label="`${item.elderName} - ${item.recordDate} - ${item.isLongTerm ? '长期用药' : '临时用药'}`"
            :value="item.id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center">
              <span>{{ item.elderName }}</span>
              <span>{{ item.recordDate }}</span>
              <el-tag :type="item.isLongTerm ? 'success' : 'warning'" size="small">
                {{ item.isLongTerm ? '长期用药' : '临时用药' }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DiseaseMedicationRelApi, DiseaseMedicationRelVO, DiseaseDiagnosisVO, MedicationRecordVO } from '@/api/elderArchives/diseasemedicationrel'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'

/** 疾病与用药关联 表单 */
defineOptions({ name: 'DiseaseMedicationRelForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  diagnosisId: undefined,
  medicationId: undefined,
})
const formRules = reactive({
  diagnosisId: [{ required: true, message: '疾病诊断不能为空', trigger: 'change' }],
  medicationId: [{ required: true, message: '用药记录不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

// 疾病诊断列表
const diagnosisList = ref<DiseaseDiagnosisVO[]>([])
// 用药记录列表
const medicationList = ref<MedicationRecordVO[]>([])

// 获取诊断类型标签
const getDiagnosisTypeLabel = (value: number) => {
  return getDictLabel(DICT_TYPE.DIAGNOSIS_TYPE, value)
}

/** 加载疾病诊断列表 */
const loadDiagnosisList = async () => {
  try {
    const res = await DiseaseMedicationRelApi.getDiseaseDiagnosisList()
    diagnosisList.value = res.list
  } catch (error) {
    console.error('加载疾病诊断列表失败', error)
  }
}

/** 加载用药记录列表 */
const loadMedicationList = async () => {
  try {
    const res = await DiseaseMedicationRelApi.getMedicationRecordList()
    medicationList.value = res.list
  } catch (error) {
    console.error('加载用药记录列表失败', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载下拉选项数据
  formLoading.value = true
  try {
    // 并行加载数据
    await Promise.all([
      loadDiagnosisList(),
      loadMedicationList()
    ])

    // 修改时，设置数据
    if (id) {
      formData.value = await DiseaseMedicationRelApi.getDiseaseMedicationRel(id)
    }
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DiseaseMedicationRelVO
    if (formType.value === 'create') {
      await DiseaseMedicationRelApi.createDiseaseMedicationRel(data)
      message.success(t('common.createSuccess'))
    } else {
      await DiseaseMedicationRelApi.updateDiseaseMedicationRel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    diagnosisId: undefined,
    medicationId: undefined,
  }
  formRef.value?.resetFields()
}
</script>
