import request from '@/config/axios'
import { CheckConflictReq } from './types'

// 任务执行人关联 VO
export interface PlanExecutorVO {
  id: number // 自增主键
  taskId: number // 关联任务ID
  executorId: number // 执行人ID
  elderId: number // 关联老人ID
  planId: number // 关联服务计划ID
  startTime: Date // 开始执行时间
  endTime: Date // 结束执行时间
  actualStart: Date // 实际开始时间
  actualEnd: Date // 实际结束时间
}

// 任务执行人关联 API
export interface ConflictInfo {
  taskId: number
  taskName: string
  date: string
  startTime: string
  endTime: string
}

// 修改执行人响应VO
export interface PlanExecutorRespVO {
  id: number
  elderId: number
  executorId: number
  planId: number
  elderName: string | null
  executorName: string | null
  planName: string | null
  startTime: string
  endTime: string
  actualStart: number
  actualEnd: number
  dateVal: string
  taskType: string
}

// 添加日历数据接口
export interface CalendarData {
  dateVal: string
  list: PlanExecutorRespVO[]
}

export const PlanExecutorApi = {
  // 查询任务执行人关联分页
  getPlanExecutorPage: async (params: any) => {
    return await request.get({ url: `/servicePlan/plan-executor/page`, params })
  },

  // 查询任务执行人关联详情
  getPlanExecutor: async (id: number) => {
    return await request.get({ url: `/servicePlan/plan-executor/get?id=` + id })
  },

  getFlictPlanExecutor: async (id: number) => {
    return await request.get({ url: `/servicePlan/plan-executor/get-flict?id=` + id })
  },

  // 新增任务执行人关联
  createPlanExecutor: async (data: PlanExecutorVO) => {
    return await request.post({ url: `/servicePlan/plan-executor/create`, data })
  },

  // 修改任务执行人关联
  updatePlanExecutor: async (data: PlanExecutorVO) => {
    return await request.put({ url: `/servicePlan/plan-executor/update`, data })
  },

  // 删除任务执行人关联
  deletePlanExecutor: async (id: number) => {
    return await request.delete({ url: `/servicePlan/plan-executor/delete?id=` + id })
  },

  // 导出任务执行人关联 Excel
  exportPlanExecutor: async (params) => {
    return await request.download({ url: `/servicePlan/plan-executor/export-excel`, params })
  },

  /** 检查老人冲突 */
  checkElderConflict(params: CheckConflictReq) {
    return request.get({ url: '/servicePlan/plan-executor/check-elder-conflict', params })
  },

  /** 检查执行人冲突 */
  checkExecutorConflict(params: CheckConflictReq) {
    return request.get({ url: '/servicePlan/plan-executor/check-executor-conflict', params })
  },

  /** 批量创建执行人 */
  batchCreatePlanExecutor(data: any) {
    return request.post({url: '/servicePlan/plan-executor/batch-create', data})
  },

  /** 检查老人冲突 */
  checkElderTimeConflict(params: {
    elderId: number
    taskId: number
    startTime: number
    endTime: number
  }) {
    return request.post({ 
      url: '/servicePlan/plan-executor/check-elder-time-conflict',
      data: params
    })
  },

  /** 检查执行人冲突 */
  checkExecutorTimeConflict(params: {
    id?: number
    executorId: number
    startTime: number
    endTime: number
  }) {
    return request.post({ 
      url: '/servicePlan/plan-executor/check-executor-time-conflict',
      data: params
    })
  },

  /** 根据任务ID删除执行人分配 */
  deleteByTaskId: async (taskId: number) => {
    return await request.delete({ 
      url: `/servicePlan/plan-executor/delete-by-task/${taskId}` 
    })
  },

  /** 按日期范围查询执行人关联 */
  getExecutorsByDateRange: async (params: {
    startDate: string
    endDate: string
    planId: number
  }): Promise<CalendarData[]> => {
    return await request.get({ 
      url: '/servicePlan/plan-executor/list-by-date-range',
      params 
    })
  },

  /** 导出执行人分配信息 */
  exportPlanExecutorExecution: async (params) => {
    return await request.download({ url: `/servicePlan/plan-executor/export-execution`, params })
  },
}
