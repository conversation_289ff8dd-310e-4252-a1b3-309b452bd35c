<template>
  <Dialog :title="'查看疾病诊断详情'" v-model="dialogVisible" width="800px">
    <div class="detail-container" v-loading="detailLoading">
      <!-- 疾病诊断基本信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>基本信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">老人姓名：</span>
                <span class="value">{{ detailData.elderName || '未关联' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断日期：</span>
                <span class="value">{{ detailData.diagnosisDate || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断医生：</span>
                <span class="value">{{ detailData.doctorName || '未知' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断机构：</span>
                <span class="value">{{ detailData.hospitalName || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断类型：</span>
                <span class="value">
                  <dict-tag v-if="detailData.diagnosisType" :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="detailData.diagnosisType" />
                  <span v-else>未知</span>
                </span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 疾病详情信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><FirstAidKit /></el-icon>
          <span>疾病详情</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">疾病编码：</span>
                <span class="value">{{ detailData.diseaseCode }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">疾病名称：</span>
                <span class="value">{{ detailData.diseaseName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">是否主要诊断：</span>
                <span class="value">
                  <dict-tag v-if="detailData.isMain !== undefined" :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="detailData.isMain ? 'true' : 'false'" />
                  <span v-else>未知</span>
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <div class="info-item">
                <span class="label">诊断描述：</span>
                <div class="value description-content" v-html="detailData.description || '无'"></div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 系统信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>系统信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">记录ID：</span>
                <span class="value">{{ detailData.id }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">诊断ID：</span>
                <span class="value">{{ detailData.diagnosisId }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(detailData.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { DiseaseDiagnosisDetailApi, DiseaseDiagnosisDetailVO } from '@/api/elderArchives/diseasediagnosisdetail'
import { DICT_TYPE } from '@/utils/dict'

/** 疾病诊断详情 */
defineOptions({ name: 'DiseaseDiagnosisDetailDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref<Partial<DiseaseDiagnosisDetailVO & {
  elderName?: string
  diagnosisDate?: string
  doctorName?: string
  hospitalName?: string
  diagnosisType?: number
}>>({}) // 详情数据

/** 打开弹窗 */
const open = async (row: DiseaseDiagnosisDetailVO) => {
  dialogVisible.value = true
  // 设置数据
  if (row.id) {
    detailLoading.value = true
    try {
      const detail = await DiseaseDiagnosisDetailApi.getDiseaseDiagnosisDetailWithRelated(row.id)
      detailData.value = detail
    } finally {
      detailLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;

  .detail-card {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-header {
      padding: 15px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      background-color: #f5f7fa;

      .el-icon {
        margin-right: 10px;
        font-size: 18px;
      }

      span {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .card-content {
      padding: 20px;

      .info-item {
        margin-bottom: 15px;

        .label {
          font-weight: bold;
          color: #606266;
        }

        .value {
          margin-left: 5px;
        }

        .description-content {
          margin-top: 10px;
          padding: 10px;
          background-color: #f8f8f8;
          border-radius: 4px;
          min-height: 50px;
        }
      }
    }
  }
}
</style>
