<template>
  <ContentWrap>
    <div class="mb-4 flex items-center">
      <!-- 标题和返回按钮 -->
      <div class="flex items-center">
        <el-button 
          @click="goBack"
          class="return-btn"
        >
          返回
        </el-button>
        <h2 class="ml-4">{{ categoryName ? `${categoryName}` : '计划分类评估关联表' }}</h2>
      </div>
    </div>
    
    <!-- 新增关联按钮 -->
    <div class="mb-4">
      <el-button
        type="primary"
        plain
        @click="openForm('create')"
        v-hasPermi="['servicePlan:service-plan-category-rel:create']"
      >新增关联
      </el-button>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="评估模板" align="center" prop="templateName" />
      <el-table-column label="有效期天数" align="center" prop="validityDays" />
      <el-table-column label="是否启用" align="center" prop="isActive">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PLAN_CATEGORY_REL_ACTIVE" :value="Number(scope.row.isActive)" />
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priority">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PLAN_CATEGORY_REL_PRIORITY" :value="scope.row.priority" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:service-plan-category-rel:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['servicePlan:service-plan-category-rel:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <servicePlanCategoryRelForm ref="formRef" @success="getList" :categoryId="categoryId" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { servicePlanCategoryRelApi, servicePlanCategoryRelVO } from '@/api/servicePlan/serviceplancategoryrel'
import servicePlanCategoryRelForm from './servicePlanCategoryRelForm.vue'
import { useRoute, useRouter } from 'vue-router'
import { DICT_TYPE } from '@/utils/dict'
/** 计划分类评估关联表 列表 */
defineOptions({ name: 'servicePlanCategoryRel' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const router = useRouter()

// 从路由参数获取分类ID和名称
const categoryId = ref(route.query.categoryId ? Number(route.query.categoryId) : undefined)
const categoryName = ref(route.query.categoryName as string)

const loading = ref(true) // 列表的加载中
const list = ref<servicePlanCategoryRelVO[]>([]) // 列表的数据

// 返回上一页
const goBack = () => {
  router.go(-1)
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    if (!categoryId.value) {
      list.value = []
      return
    }
    
    // 使用根据分类ID查询的API
    const data = await servicePlanCategoryRelApi.listByCategoryId(categoryId.value)
    list.value = data || []
  } finally {
    loading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await servicePlanCategoryRelApi.deleteservicePlanCategoryRel(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.ml-4 {
  margin-left: 1rem;
}

/* 返回按钮样式 */
.return-btn {
  padding: 8px 16px;
  border: 1px solid var(--el-border-color-lighter);
  background-color: #f5f7fa;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s;
}

.return-btn:hover {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary-light-7);
  background-color: var(--el-color-primary-light-9);
}
</style>
