<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="老人姓名" prop="elderName">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择老人"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="item in elderProfiles"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="记录日期" prop="recordDate">
        <el-date-picker
          v-model="queryParams.recordDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="记录人员" prop="recorderName">
        <el-input
          v-model="queryParams.recorderName"
          placeholder="请输入记录人员"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="长期用药" prop="isLongTerm">
        <el-select
          v-model="queryParams.isLongTerm"
          placeholder="请选择是否长期用药"
          clearable
          class="!w-240px"
        >
          <el-option :label="'是'" :value="true" />
          <el-option :label="'否'" :value="false" />
        </el-select>
      </el-form-item>

      <!-- 高级搜索区域 -->
      <el-collapse-transition>
        <div v-if="showAdvanced">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="queryParams.startDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="queryParams.endDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="queryParams.remark"
              placeholder="请输入备注"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <el-date-picker
              v-model="queryParams.createTime"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              class="!w-240px"
            />
          </el-form-item>
        </div>
      </el-collapse-transition>

      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button @click="showAdvanced = !showAdvanced" type="info" link>
          {{ showAdvanced ? '收起' : '展开' }}
          <Icon :icon="showAdvanced ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-5px" />
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['elderArchives:medication-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['elderArchives:medication-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="老人姓名" align="center" prop="elderName" min-width="100">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.elderName || '未知' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="记录日期" align="center" prop="recordDate" min-width="100">
        <template #default="scope">
          {{ formatDate(scope.row.recordDate) }}
        </template>
      </el-table-column>
      <el-table-column label="记录人员" align="center" prop="recorderName" min-width="100" />
      <el-table-column label="用药类型" align="center" prop="isLongTerm" min-width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isLongTerm ? 'success' : 'info'">
            {{ scope.row.isLongTerm ? '长期用药' : '临时用药' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用药时间" align="center" min-width="180">
        <template #default="scope">
          <div v-if="scope.row.isLongTerm">
            {{ formatDate(scope.row.startDate) }} 至 {{ formatDate(scope.row.endDate) || '长期' }}
          </div>
          <div v-else>
            {{ formatDate(scope.row.recordDate) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="180" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDetail(scope.row.id)"
            v-hasPermi="['elderArchives:medication-record:query']"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['elderArchives:medication-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:medication-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <MedicationRecordForm ref="formRef" @success="getList" />

  <!-- 详情弹窗 -->
  <MedicationRecordDetail ref="detailRef" />
</template>

<script setup lang="ts">
import { dateFormatter, formatDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { MedicationRecordApi, MedicationRecordVO } from '@/api/elderArchives/medicationrecord'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import { DICT_TYPE } from '@/utils/dict'
import MedicationRecordForm from './MedicationRecordForm.vue'
import MedicationRecordDetail from './MedicationRecordDetail.vue'

/** 老人用药记录 列表 */
defineOptions({ name: 'MedicationRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<MedicationRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([]) // 老人列表
const showAdvanced = ref(false) // 是否显示高级搜索
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  recordDate: [],
  recorderName: undefined,
  isLongTerm: undefined,
  startDate: [],
  endDate: [],
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MedicationRecordApi.getMedicationRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看详情操作 */
const detailRef = ref()
const openDetail = (id: number) => {
  detailRef.value.open(id)
}

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MedicationRecordApi.deleteMedicationRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await MedicationRecordApi.exportMedicationRecord(queryParams)
    download.excel(data, '老人用药记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getElderProfiles()
  await getList()
})
</script>