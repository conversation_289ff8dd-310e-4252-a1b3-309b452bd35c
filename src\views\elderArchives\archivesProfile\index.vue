<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idNumber">
        <el-input v-model="queryParams.idNumber" placeholder="请输入身份证号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="文化程度" prop="educationLevel">
        <el-select v-model="queryParams.educationLevel" placeholder="请选择文化程度" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.EDUCATION_LEVEL)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="婚姻状况" prop="maritalStatus">
        <el-select v-model="queryParams.maritalStatus" placeholder="请选择婚姻状况" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.MARITAL_STATUS)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="户籍类型" prop="householdType">
        <el-select v-model="queryParams.householdType" placeholder="请选择户籍类型" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.HOUSEHOLD_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="居住类型" prop="residenceType">
        <el-select 
          v-model="queryParams.residenceType" 
          multiple 
          placeholder="请选择居住类型" 
          class="w-full"
          :multiple-limit="5"
        >
          <el-option 
            v-for="dict in getIntDictOptions(DICT_TYPE.RESIDENCE_TYPE)" 
            :key="dict.value" 
            :label="dict.label"
            :value="dict.value" 
          />
        </el-select>
        <!-- 添加标签展示区域 -->
        <div class="selected-tags mt-2">
          <el-tag
            v-for="type in queryParams.residenceType"
            :key="type"
            class="mr-1 mb-1"
            type="success"
            effect="plain"
            size="small"
          >
            {{ getDictLabel(DICT_TYPE.RESIDENCE_TYPE, type) }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="出生日期" prop="birthDate">
        <el-date-picker v-model="queryParams.birthDate" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="queryParams.contactPhone" placeholder="请输入联系电话" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="服务等级" prop="serviceLevel">
        <el-select v-model="queryParams.serviceLevel" placeholder="请选择服务等级" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_SERVICE_LEVEL)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')"
          v-hasPermi="['elderArchives:archives-profile:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading"
          v-hasPermi="['elderArchives:archives-profile:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="头像" align="center" width="100">
        <template #default="scope">
          <el-avatar :size="50" :src="scope.row.avatar || '/default-avatar.png'" />
        </template>
      </el-table-column>
      <el-table-column label="服务等级" align="center" prop="serviceLevel">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ELDER_SERVICE_LEVEL" :value="scope.row.serviceLevel" />
        </template>
      </el-table-column>
      <el-table-column label="身份证号" align="center">
        <template #default="{ row }">
          <el-tooltip
            :content="row.idNumber"
            placement="top"
            :show-after="0"
            :hide-after="0"
            effect="light"
          >
            <span>{{ formatIdNumber(row.idNumber) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column label="户籍类型" align="center" prop="householdType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HOUSEHOLD_TYPE" :value="scope.row.householdType" />
        </template>
      </el-table-column>
      <el-table-column label="居住类型" align="center" prop="residenceType">
        <template #default="scope">
          <div v-if="scope.row.residenceType">
            <dict-tag 
              v-for="item in scope.row.residenceType.split(',')" 
              :key="item" 
              :type="DICT_TYPE.RESIDENCE_TYPE" 
              :value="parseInt(item)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="医疗保险" align="center" prop="medicalInsurance">
        <template #default="scope">
          <div v-if="scope.row.medicalInsurance">
            <dict-tag v-for="item in scope.row.medicalInsurance.split(',')" 
                     :key="item" 
                     :type="DICT_TYPE.MEDICAL_INSURANCE" 
                     :value="parseInt(item)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="出生日期" align="center" prop="birthDate">
        <template #default="scope">
          {{ formatBirthDate(scope.row.birthDate) }}
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="contactPhone">
        <template #default="{ row }">
          <el-tooltip
            :content="row.contactPhone"
            placement="top"
            :show-after="0"
            :hide-after="0"
            effect="light"
          >
            <span>{{ formatPhoneNumber(row.contactPhone) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['elderArchives:archives-profile:query']"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['elderArchives:archives-profile:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:archives-profile:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ArchivesProfileForm ref="formRef" @success="getList" @submit="handleSubmit" />

  <!-- 详情弹窗 -->
  <ArchivesProfileDetail ref="detailRef" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile'
import ArchivesProfileForm from './ArchivesProfileForm.vue'
import ArchivesProfileDetail from './ArchivesProfileDetail.vue'

/** 老人基础信息 列表 */
defineOptions({ name: 'ArchivesProfile' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ArchivesProfileVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  idNumber: undefined,
  gender: undefined,
  birthDate: [],
  contactPhone: undefined,
  serviceLevel: undefined,
  educationLevel: undefined,
  maritalStatus: undefined,
  householdType: undefined,
  residenceType: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const formData = ref({}) // 表单数据对象

const detailRef = ref() // 详情 Ref

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ArchivesProfileApi.getArchivesProfilePage(queryParams)
    list.value = data.list
    total.value = data.total
    if (data.list && data.list.length > 0) {
      handleGetData(data.list[0])
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看按钮操作 */
const handleDetail = async (row: ArchivesProfileVO) => {
  detailRef.value.open(row.id)
}

/** 编辑按钮操作 */
const handleUpdate = (row: ArchivesProfileVO) => {
  openForm('update', row.id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ArchivesProfileApi.deleteArchivesProfile(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 处理多选字段，确保其为字符串格式
    const exportParams: any = { ...queryParams }
    if (exportParams.residenceType && Array.isArray(exportParams.residenceType)) {
      exportParams.residenceType = exportParams.residenceType.join(',')
    }
    // 发起导出
    exportLoading.value = true
    const data = await ArchivesProfileApi.exportArchivesProfile(exportParams)
    download.excel(data, '老人基础信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

/** 格式化出生日期 */
const formatBirthDate = (birthDate: any): string => {
  if (!birthDate) return ''
  if (Array.isArray(birthDate)) {
    return `${birthDate[0]}-${String(birthDate[1]).padStart(2, '0')}-${String(birthDate[2]).padStart(2, '0')}`
  }
  return birthDate
}

/**
 * 格式化身份证号（脱敏处理）
 * 规则：只隐藏后四位数字
 * @param value 原始身份证号
 * @returns 脱敏后的身份证号
 */
const formatIdNumber = (value: string) => {
  if (!value) return ''
  if (value.length !== 18) return value
  return `${value.substring(0, 14)}****`
}

/**
 * 格式化手机号（脱敏处理）
 * 规则：保留前3位和后4位，中间用*号代替
 * @param value 原始手机号
 * @returns 脱敏后的手机号
 */
const formatPhoneNumber = (value: string) => {
  if (!value) return ''
  if (value.length !== 11) return value
  return `${value.substring(0, 3)}****${value.substring(7)}`
}

/**
 * 格式化金额
 */
const formatMoney = (value: number): string => {
  if (!value) return '0.00'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

/** 在提交表单前处理数据 */
const handleSubmit = async (data) => {
  // 检查数据中是否存在居住类型数组，如果有则转换为逗号分隔的字符串
  if (data.residenceType && Array.isArray(data.residenceType)) {
    data.residenceType = data.residenceType.join(',')
  }
  // 表单已经在组件内部提交，这里不需要再次提交
};

/** 在获取数据后处理 */
const handleGetData = (data) => {
  if (!data) return
  
  // 将逗号分隔的字符串转换为数组
  formData.value = {
    ...data,
    residenceType: data.residenceType ? data.residenceType.split(',') : []
  };
};

// 校验规则
const rules = {
  residenceType: [
    { required: true, message: '请选择居住类型', trigger: 'change' },
    { type: 'array', message: '居住类型必须为数组' }
  ],
  householdType: [
    { required: true, message: '请选择户籍类型', trigger: 'change' }
  ]
};
</script>

<style scoped>
/* 添加悬停样式 */
.el-table span {
  cursor: pointer;
}

/* 可选：添加一个小图标表示可以查看完整信息 */
.el-table span:hover::after {
  content: '👁️';
  margin-left: 4px;
  font-size: 12px;
}

.remark-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.selected-tags {
  margin-top: 8px;
  min-height: 32px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 优化选择框样式 */
:deep(.el-select) {
  width: 100%;
}

:deep(.el-select__tags) {
  flex-wrap: wrap;
}
</style>