import request from '@/config/axios'

// 评估任务清单执行 VO
export interface ListExecutionVO {
  id?: number // ID
  listId: number // 评估任务清单ID
  listName: string // 评估任务清单名称
  elderId: number // 老人ID
  elderName: string // 老人姓名
  evaluatorId: number // 评估师ID
  evaluatorName: string // 评估师姓名
  evaluationReason: string // 评估原因
  status: number // 状态
  startTime: Date // 开始时间
  endTime?: Date // 完成时间
  requiredTemplateIds: string // 所需完成的评估模板ID
  completedTemplateIds?: string // 已完成的评估模板ID
  resultIds?: string // 关联的评估结果ID
  apiKey?: string // api密钥
  evaluatorAnalysis?: string // 评估师分析
  aiInputs?: string // AI输入
  aiAnalysis?: string // AI分析
  summaryResult?: string // 综合评估结果
}

// 评估任务清单执行 API
export const ListExecutionApi = {
  // 查询评估任务清单执行分页
  getListExecutionPage: async (params: any) => {
    return await request.get({ url: `/evaluation/list-execution/page`, params })
  },

  // 查询评估任务清单执行详情
  getListExecution: async (id: number) => {
    return await request.get({ url: `/evaluation/list-execution/get?id=` + id })
  },

  // 新增评估任务清单执行
  createListExecution: async (data: ListExecutionVO) => {
    return await request.post({ url: `/evaluation/list-execution/create`, data })
  },

  // 修改评估任务清单执行
  updateListExecution: async (data: ListExecutionVO) => {
    return await request.put({ url: `/evaluation/list-execution/update`, data })
  },

  // 删除评估任务清单执行
  deleteListExecution: async (id: number) => {
    return await request.delete({ url: `/evaluation/list-execution/delete?id=` + id })
  },

  // 导出评估任务清单执行 Excel
  exportListExecution: async (params) => {
    return await request.download({ url: `/evaluation/list-execution/export-excel`, params })
  }
}
