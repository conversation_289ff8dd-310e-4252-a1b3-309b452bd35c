<template>
  <div class="create-task">
    <!-- 修改顶部按钮区域 -->
    <div class="view-switch">
      <div class="left-buttons">
        <el-button type="primary" @click="handleCreate">
          新建任务
        </el-button>
        <el-button @click="exportWord">
          导出word
        </el-button>
      </div>
      <el-radio-group v-model="currentView" size="large">
        <el-radio-button value="calendar">日历视图</el-radio-button>
        <el-radio-button value="plan">计划信息</el-radio-button>
      </el-radio-group>
    </div>
    <!-- 日历视图 -->
    <div v-show="currentView === 'calendar'" class="schedule-picker">
      <div class="schedule-picker__content">
        <el-calendar v-model="currentDate" >
          <template #header="{ date }">
            <div class="calendar-header">
              <el-button-group>
                <el-button size="small" @click="selectToday">今天</el-button>
                <el-button size="small" @click="prevMonth">上月</el-button>
                <el-button size="small" @click="nextMonth">下月</el-button>
              </el-button-group>
              <span class="calendar-header__title">
                {{date}}
              </span>
            </div>
          </template>
          <template #date-cell="{ data }">
            <div class="calendar-cell">
              <div class="date-header">
                {{ data.day.split('-')[2] }}
              </div>
              <div class="event-list">
                <template v-for="event in getEventsForDate(data)" :key="event.id">
                  <div 
                    class="schedule-item"
                    @click="handleEventClick(event)"
                  >
                    <div class="time-range">
                      {{ event.startTime }} - {{ event.endTime }}
                    </div>
                    <div class="schedule-content">
                      <div class="names">
                        <span class="">{{ event.taskType || '--' }}</span>
                        <span class="executor-name">{{ event.executorName || '--' }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </div>

    <!-- 计划信息视图 -->
    <div v-show="currentView === 'plan'">
      <el-descriptions v-loading="planLoading" title="服务计划信息" :column="3" border>
        <el-descriptions-item label="计划名称" label-width="80px">{{ planInfo.planName }}</el-descriptions-item>
        <el-descriptions-item label="计划分类" label-width="80px">{{ planInfo.categoryName }}</el-descriptions-item>
        <el-descriptions-item label="老人姓名" label-width="80px">{{ planInfo.elderName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_STATUS" :value="planInfo.status" />
        </el-descriptions-item>
        <el-descriptions-item label="开始日期">{{ planInfo.startDate }}</el-descriptions-item>
        <el-descriptions-item label="结束日期">{{ planInfo.endDate }}</el-descriptions-item>
        <el-descriptions-item label="关联评估结果">{{ evaluationResult.join(', ') }}</el-descriptions-item>
        <el-descriptions-item label="计划描述">
          <div class="description-wrapper">
            <div class="markdown-content" v-html="renderMarkdown(planInfo.description)"></div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 使用新的任务列表组件 -->
    <TaskList
      ref="taskListRef"
      @edit="handleEdit"
      :plan-id="Number(route.query.planId)"
    />

    <!-- 创建任务弹窗 -->
    <CreateTaskDialog
      v-model:visible="dialogVisible"
      :selected-date="selectedDate"
      :task-id="editingTaskId"
      @success="handleCreateSuccess"
      :plan-info="planInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import dayjs from 'dayjs'
import { PlanExecutorApi  } from '@/api/servicePlan/planexecutor'
import TaskList from './components/TaskList.vue'
import CreateTaskDialog from './components/CreateTaskDialog.vue'
import { DICT_TYPE } from '@/utils/dict'
import { ServicePlanApi } from '@/api/servicePlan/serviceplan'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import { ResultApi } from '@/api/evaluation/result'
import download from '@/utils/download'
import MarkdownIt from 'markdown-it'


defineOptions({
  name: 'ServicePlanTaskCreate'
})

const message = useMessage() // 消息弹窗
const route = useRoute()
const router = useRouter()

// 只保留日历相关的状态和方法
const currentDate = ref(new Date())
const selectedDate = ref<Date | undefined>(undefined)
const dialogVisible = ref(false)

// 添加编辑相关的状态
const editingTaskId = ref<number>()

// 修改月度执行人关联数据类型
const monthExecutors = ref<CalendarData[]>([])

// 服务计划信息
interface PlanInfo {
  id?: number
  categoryName?: string
  elderId?: number
  elderName?: string
  planName?: string
  status: number // 修改为必需且有默认值
  startDate?: string
  endDate?: string
  description?: string // 添加计划描述
}

const planInfo = ref<PlanInfo>({
  id: undefined,
  categoryName: undefined,
  elderId: undefined,
  elderName: undefined,
  planName: undefined,
  status: 0, // 给status一个默认值
  startDate: undefined,
  endDate: undefined,
  description: undefined // 初始化计划描述
})
const planLoading = ref(false)

// 添加视图切换状态
const currentView = ref<'calendar' | 'plan'>('plan')

// 日历相关方法
const selectToday = () => {
  currentDate.value = new Date()
}

const prevMonth = () => {
  const date = dayjs(currentDate.value).subtract(1, 'month')
  currentDate.value = date.toDate()
}

const nextMonth = () => {
  const date = dayjs(currentDate.value).add(1, 'month')
  currentDate.value = date.toDate()
}

const openCreateDialog = (taskId?: number) => {
  editingTaskId.value = taskId
  dialogVisible.value = true
}

const taskListRef = ref()



/** 编辑按钮操作 */
const handleEdit = (taskId: number) => {
  editingTaskId.value = taskId
  dialogVisible.value = true
}

/** 创建成功后的回调 */
const handleCreateSuccess = async () => {
  editingTaskId.value = undefined
  // 刷新日历数据
  await getMonthExecutors(currentDate.value)
  // 刷新任务列表
  taskListRef.value?.getList()
}


// 修改 CalendarData 类型定义,移除 list 属性要求
interface CalendarEvent {
  id: number
  elderId: number
  executorId: number
  planId: number
  elderName: string | null
  executorName: string | null
  planName: string | null
  startTime: string
  endTime: string
  actualStart: number
  actualEnd: number
  dateVal: string
  taskType: string
}

interface CalendarData {
  dateVal: string
  list: CalendarEvent[]
}

// 修改事件点击处理方法的类型
const handleEventClick = (event: CalendarEvent) => {
  console.log('事件点击:', event)
}

// 修改新建任务的处理方法
const handleCreate = () => {
  selectedDate.value = undefined // 改用 undefined 而不是 null
  editingTaskId.value = undefined
  openCreateDialog()
}

// 获取月度执行人关联数据
const getMonthExecutors = async (date: Date) => {
  const planId = Number(route.query.planId)
  if (!planId) {
    return
  }

  try {
    const response = await PlanExecutorApi.getExecutorsByDateRange({
      startDate: dayjs(date).startOf('month').format('YYYY-MM-DD'),
      endDate: dayjs(date).endOf('month').format('YYYY-MM-DD'),
      planId
    })
    // 确保数据格式正确
    monthExecutors.value = response
  } catch (error) {
    console.error('获取月度执行人关联数据失败:', error)
    monthExecutors.value = []
  }
}

// 获取日期的执行人关联数据
const getEventsForDate = (data: { day: string }) => {
  // console.log('日历单元格数据:', data)
  
  if (!monthExecutors.value || !Array.isArray(monthExecutors.value)) {
    console.log('monthExecutors 为空或不是数组:', monthExecutors.value)
    return []
  }

  // 找到匹配日期的数据
  const dayData = monthExecutors.value.find(event => event.dateVal === data.day)
  
  if (dayData?.list) {
    return dayData.list
  }
  
  return []
}

// 监听月份变化
watch(
  () => currentDate.value,
  async (newDate) => {
    if (newDate) {
      console.log('日期变化，重新获取数据:', dayjs(newDate).format('YYYY-MM'))
      await getMonthExecutors(newDate)
    }
  },
  { 
    immediate: true,
    deep: true 
  }
)

// 添加评估结果的状态
const evaluationResult = ref<string[]>([])

// 获取评估信息的方法
const getEvaluationInfo = async (planId: number) => {
  try {
    const data = await ResultApi.selectEvaluationByPlanId(planId)
    evaluationResult.value = data // 假设评估结果在 data.result 中
  } catch (error) {
    console.error('获取评估信息失败:', error)
    message.error('获取评估信息失败')
  }
}

// 获取服务计划信息
const getPlanInfo = async (id: number) => {
  planLoading.value = true
  try {
    const data = await ServicePlanApi.getServicePlan(id)
    planInfo.value = {
      ...data,
      description: data.description || '' // 确保将描述包含在内
    }
  } catch (error) {
    message.error('获取服务计划信息失败')
    // 获取失败时返回列表页
    router.push('/servicePlan/service-plan')
  } finally {
    planLoading.value = false
  }
}

const exportWord = async () => {
  console.log('导出 Word 文件')
  const planId = Number(route.query.planId)
  if (!planId) {
    message.error('服务计划ID不能为空')
    router.push('/servicePlan/service-plan')
    return
  }
  try {
    const data = await ServicePlanApi.exportWord({
      planId: Number(route.query.planId)
    })
    // 获取当前日期，格式为 YYYY-MM-DD
    const currentDate = dayjs().format('YYYY-MM-DD')
    // 拼接文件名：计划名称_老人姓名_日期.docx
    const fileName = `${planInfo.value.planName}_${planInfo.value.elderName}_${currentDate}.docx`
    download.word(data, fileName)
  } catch (error) {
    message.error('导出失败')
  }
}

// 初始化 markdown-it
const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true
})

// 转换 Markdown 为 HTML
const renderMarkdown = (content: string) => {
  return md.render(content || '')
}

onMounted(async () => {
  await getMonthExecutors(currentDate.value)

  const planId = Number(route.query.planId)
  if (!planId) {
    message.error('服务计划ID不能为空')
    router.push('/servicePlan/service-plan')
    return
  }
  
  await Promise.all([
    getPlanInfo(planId),
    getEvaluationInfo(planId) // 添加获取评估信息
  ])
})
</script>

<style lang="scss" scoped>
.create-task {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.schedule-picker {
  &__header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }

  &__content {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;

  &__title {
    font-size: 16px;
    font-weight: 500;
    min-width: 120px;
    text-align: right;
  }
}

.calendar-cell {
  height: 100%;
  min-height: 100px;
  padding: 4px;
  
  .date-header {
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .event-list {
    overflow-y: auto;
    max-height: calc(100% - 24px);
  }
}

.schedule-item {
  margin: 4px 0;
  padding: 6px 8px;
  border-radius: 4px;
  background: var(--el-fill-color-light);
  cursor: pointer;
  
  &:hover {
    background: var(--el-fill-color);
  }
  
  .time-range {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--el-color-primary);
    margin-bottom: 4px;
  }
  
  .schedule-content {
    font-size: 12px;
    
    .names {
      display: flex;
      gap: 8px;
      margin-bottom: 2px;
      
      .executor-name {
        color: var(--el-text-color-secondary);
        
        &:before {
          content: '|';
          margin-right: 4px;
          opacity: 0.6;
        }
      }
    }
    
  }
}

:deep(.vc-container) {
  --vc-border-color: var(--el-border-color);
  --vc-bg-color: transparent;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
}

:deep(.vc-weeks) {
  padding: 0;
}

:deep(.vc-day) {
  min-height: 120px;
  padding: 0;
}

.selected-date {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

:deep(.el-calendar) {
  --el-calendar-header-height: auto;
  background-color: transparent;
}

:deep(.el-calendar__header) {
  padding: 0;
}

:deep(.el-calendar-table .el-calendar-day) {
  height: auto;
  padding: 0;
  min-height: 120px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-picker) {
  width: 100%;
}

.mr-2 {
  margin-right: 8px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-2 {
  margin-top: 8px;
}

.text-gray-500 {
  color: #666;
}

.text-sm {
  font-size: 14px;
}

:deep(.el-time-picker) {
  width: 180px;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.el-checkbox {
  margin-right: 0;
}

.ml-2 {
  margin-left: 8px;
}

.task-dates-preview {
  padding: 16px;
  border-left: 1px solid var(--el-border-color);
}

.dates-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 500px;
  overflow-y: auto;
}

:deep(.el-checkbox) {
  width: 100%;
  margin-right: 0;
  
  .el-checkbox__label {
    width: 100%;
  }
}

.w-300px {
  width: 300px;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.mr-4 {
  margin-right: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}

:deep(.date-picker-readonly) {
  .el-date-picker__header-label {
    font-weight: bold;
  }
  
  .el-picker-panel__content {
    .el-date-table td {
      pointer-events: none;
    }
  }

  .el-date-picker__header-label,
  .el-picker-panel__icon-btn {
    pointer-events: none;
  }
}

.conflict-check {
  border-top: 1px solid var(--el-border-color);
  padding-top: 16px;

  .conflict-check-btn {
    width: 100%;
    margin: 0px;
  }
  
  .conflict-check-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .conflict-details {
    padding: 12px;
    background-color: var(--el-color-warning-light-9);
    border-radius: 4px;
  }

  .conflict-item {
    padding: 8px;
    background-color: var(--el-color-warning-light-8);
    border-radius: 4px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .conflict-text {
    color: var(--el-color-warning-darker);
    margin: 0;
    line-height: 1.6;
    white-space: pre-line;
    font-size: 13px;
  }
}

.text-base {
  font-size: 16px;
}

// 添加任务列表相关样式
.task-list {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 24px;

  &__header {
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 16px 0;
    }
  }

  &__search {
    .el-form-item {
      margin-bottom: 16px;
    }
  }

  &__table {
    margin-bottom: 16px;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.view-switch {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-buttons {
    display: flex;
    gap: 8px;
  }
}
.is-selected {
  color: #1989fa;
}

/* 添加 WangEditor 样式 */
.wang-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

// 调整分页组件的位置
:deep(.el-pagination) {
  margin: 16px 0 24px;
  justify-content: center;
}

.description-wrapper {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: var(--el-fill-color-lighter);
    border-radius: 3px;
  }
}

.markdown-content {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  
  :deep(h1, h2, h3, h4, h5, h6) {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }
  
  :deep(p) {
    margin-bottom: 16px;
    line-height: 1.5;
  }
  
  :deep(ul, ol) {
    padding-left: 2em;
    margin-bottom: 16px;
  }
  
  :deep(code) {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27,31,35,0.05);
    border-radius: 3px;
  }
  
  :deep(pre) {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;
  }
}
</style>