<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="任务执行ID" prop="taskExecutorId">
        <el-input v-model="formData.taskExecutorId" placeholder="请输入任务执行人关联ID" />
      </el-form-item>
      <el-form-item label="冲突的任务" prop="taskId">
        <el-input v-model="formData.taskId" placeholder="请输入关联冲突的任务ID" />
      </el-form-item>
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="formData.executorId"
          placeholder="请选择执行人"
          clearable
          filterable
          class="w-full"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          >
            <span>{{ user.nickname }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="冲突类型" prop="conflictType">
        <el-select v-model="formData.conflictType" placeholder="请选择冲突类型" class="w-full">
          <el-option label="时间冲突" value="time" />
          <el-option label="执行人冲突" value="executor" />
        </el-select>
      </el-form-item>
      <el-form-item label="冲突详情" prop="conflictData">
        <el-input 
          v-model="formData.conflictData" 
          placeholder="请输入冲突详情" 
          type="textarea" 
          :rows="4"
        />
      </el-form-item>
      <el-form-item label="解决状态" prop="resolveStatus">
        <el-radio-group v-model="formData.resolveStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_CONFLICT_LOG_RESOLVE_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="解决备注" prop="resolveComment">
        <el-input 
          v-model="formData.resolveComment" 
          placeholder="请输入解决备注" 
          type="textarea"
          :rows="4" 
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ConflictLogApi, ConflictLogVO } from '@/api/servicePlan/serviceplanconflictlog'

/** 任务冲突日志 表单 */
defineOptions({ name: 'ConflictLogForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  taskExecutorId: undefined,
  taskId: undefined,
  executorId: undefined,
  conflictType: undefined,
  conflictData: undefined,
  resolveStatus: undefined,
  resolveComment: undefined,
})
const formRules = reactive({
  taskExecutorId: [{ required: true, message: '任务执行人关联ID不能为空', trigger: 'blur' }],
  taskId: [{ required: true, message: '关联冲突的任务ID不能为空', trigger: 'blur' }],
  conflictType: [{ required: true, message: '冲突类型不能为空', trigger: 'change' }],
  conflictData: [{ required: true, message: '冲突详情不能为空', trigger: 'blur' }],
  resolveStatus: [{ required: true, message: '解决状态不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

// 定义 props
const props = defineProps({
  userList: {
    type: Array,
    required: true
  }
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ConflictLogApi.getConflictLog(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ConflictLogVO
    if (formType.value === 'create') {
      await ConflictLogApi.createConflictLog(data)
      message.success(t('common.createSuccess'))
    } else {
      await ConflictLogApi.updateConflictLog(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    taskExecutorId: undefined,
    taskId: undefined,
    executorId: undefined,
    conflictType: undefined,
    conflictData: undefined,
    resolveStatus: undefined,
    resolveComment: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.w-full {
  width: 100%;
}
</style>