import request from '@/config/axios'

// 评估任务清单 VO
export interface ListVO {
  id: number // ID
  name: string // 名称
  description: string // 描述
  type: string // 类型
  status: number // 状态
  templateIds: string // 关联的评估模板ID
}

// 评估任务清单 API
export const ListApi = {
  // 查询评估任务清单分页
  getListPage: async (params: any) => {
    return await request.get({ url: `/evaluation/list/page`, params })
  },

  // 查询评估任务清单详情
  getList: async (id: number) => {
    return await request.get({ url: `/evaluation/list/get?id=` + id })
  },

  // 新增评估任务清单
  createList: async (data: ListVO) => {
    return await request.post({ url: `/evaluation/list/create`, data })
  },

  // 修改评估任务清单
  updateList: async (data: ListVO) => {
    return await request.put({ url: `/evaluation/list/update`, data })
  },

  // 删除评估任务清单
  deleteList: async (id: number) => {
    return await request.delete({ url: `/evaluation/list/delete?id=` + id })
  },

  // 导出评估任务清单 Excel
  exportList: async (params) => {
    return await request.download({ url: `/evaluation/list/export-excel`, params })
  },
}
