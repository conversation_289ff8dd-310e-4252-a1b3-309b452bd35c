<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-input
          v-model="queryParams.type"
          placeholder="请输入类型"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_LIST_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['evaluation:list:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['evaluation:list:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column type="index" label="序号" width="60" align="center" /> -->
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="类型" align="center" prop="type" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATION_LIST_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="关联的评估模板" align="center" prop="templateIds">
        <template #default="scope">
          {{ formatTemplateNames(scope.row.templateIds) }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['evaluation:list:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['evaluation:list:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ListForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ListApi, ListVO } from '@/api/evaluation/list'
import { TemplateApi } from '@/api/evaluation/template'
import ListForm from './ListForm.vue'

defineOptions({ name: 'List' })

// 消息弹窗
const message = useMessage()
// 国际化
const { t } = useI18n()

// 列表的加载
const loading = ref(true)
// 列表的数据
const list = ref<ListVO[]>([])
// 列表的总页数
const total = ref(0)
// 搜索的参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  description: undefined,
  type: undefined,
  status: undefined,
  templateIds: undefined,
  createTime: []
})
// 搜索的表单
const queryFormRef = ref()
// 导出的加载
const exportLoading = ref(false)
// 模板ID到名称的映射
const templateMap = ref<Record<string, string>>({})

/** 获取模板列表 */
const getTemplateOptions = async () => {
  try {
    const res = await TemplateApi.getSimpleTemplateList()
    if (res && Array.isArray(res)) {
      // 创建ID到名称的映射
      templateMap.value = res.reduce((acc, item) => {
        acc[item.id] = item.name
        return acc
      }, {})
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

/** 模板名称格式化方法 */
const formatTemplateNames = (templateIds: string) => {
  if (!templateIds) return '-'
  const ids = templateIds.split(',')
  return ids.map((id) => templateMap.value[id] || id).join(', ')
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取模板列表
    await getTemplateOptions()

    const data = await ListApi.getListPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ListApi.deleteList(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ListApi.exportList(queryParams)
    download.excel(data, '评估任务清单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
