<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="护理计划" prop="planId">
        <el-select
          v-model="formData.planId"
          placeholder="请选择护理计划"
          clearable
          class="w-full"
        >
          <el-option
            v-for="item in planList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-select
          v-model="formData.elderId"
          placeholder="请选择关联老人"
          clearable
          class="w-full"
        >
          <el-option
            v-for="item in elderList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入任务名称"
          clearable
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="任务分类" prop="categoryId">
        <el-tree-select
          v-model="formData.categoryId"
          :data="categoryTree"
          :props="categoryProps"
          placeholder="请选择任务分类"
          clearable
          @update:model-value="handleCategoryChange"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否周期性" prop="isRecurring">
        <el-radio-group v-model="formData.isRecurring">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="周期性规则" prop="repeatPattern">
        <el-input v-model="formData.repeatPattern" placeholder="请输入周期性规则" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-time-picker
          v-model="formData.startTime"
          value-format="HH:mm"
          format="HH:mm"
          placeholder="选择开始时间"
          clearable
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-time-picker
          v-model="formData.endTime"
          value-format="HH:mm"
          format="HH:mm"
          placeholder="选择结束时间"
          clearable
        />
      </el-form-item>
      <el-form-item label="执行人" prop="recommendedExecutors">
        <el-select
          v-model="selectedExecutors"
          multiple
          placeholder="请选择执行人"
          clearable
          class="w-full"
          @change="handleExecutorsChange"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行细则" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'

/** 服务任务 表单 */
defineOptions({ name: 'TaskForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 添加分类相关的数据
const categoryTree = ref<any[]>([])
const categoryProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true,
}

// 修改表单数据，添加分类字段
const formData = ref({
  id: undefined,
  planId: undefined,
  elderId: undefined,
  name: undefined,
  categoryId: undefined,
  taskType: undefined, // 用于存储分类名称
  status: undefined,
  isRecurring: undefined as number | undefined,
  repeatPattern: undefined,
  startTime: undefined,
  endTime: undefined,
  recommendedExecutors: undefined,
  priority: undefined,
  description: undefined,
})

// 添加分类相关的表单验证
const formRules = reactive({
  name: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  planId: [{ required: true, message: '关联护理计划ID不能为空', trigger: 'blur' }],
  elderId: [{ required: true, message: '关联老人表不能为空', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择任务分类', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  isRecurring: [{ required: true, message: '是否周期任务不能为空', trigger: 'blur' }],
  startTime: [{ required: true, message: '任务开始时间点不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '任务结束时间点不能为空', trigger: 'blur' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'change' }],
  description: [{ required: true, message: '执行细则不能为空', trigger: 'blur' }],
})

const formRef = ref() // 表单 Ref

interface Props {
  userList: SelectOption[]
  elderList: SelectOption[]
  planList: SelectOption[]
}

const props = defineProps<Props>()

// 选中的执行人
const selectedExecutors = ref<number[]>([])

/** 处理执行人变化 */
const handleExecutorsChange = (values: number[]) => {
  formData.value.recommendedExecutors = values.length > 0 ? JSON.stringify(values) : undefined
}

/** 处理分类变化 */
const handleCategoryChange = (categoryId: number | null) => {
  if (!categoryId) {
    formData.value.categoryId = undefined
    formData.value.taskType = undefined
    return
  }
  
  // 查找分类名称
  const findCategory = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return node
      }
      if (node.children) {
        const found = findCategory(node.children)
        if (found) return found
      }
    }
    return null
  }

  const category = findCategory(categoryTree.value)
  formData.value.categoryId = categoryId
  formData.value.taskType = category?.name
}

/** 获取分类树数据 */
const getCategoryTree = async () => {
  try {
    const treeData = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = treeData || []
  } catch (error) {
    console.error('获取分类树失败:', error)
    message.error('获取分类树失败')
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 获取分类树数据
  await getCategoryTree()
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await TaskApi.getTask(id)
      formData.value = {
        ...data,
        isRecurring: Number(data.isRecurring)
      }
      // 设置选中的执行人
      if (data.recommendedExecutors) {
        try {
          selectedExecutors.value = JSON.parse(data.recommendedExecutors)
        } catch (e) {
          console.error('解析执行人数据失败:', e)
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TaskVO
    if (formType.value === 'create') {
      await TaskApi.createTask(data)
      message.success(t('common.createSuccess'))
    } else {
      await TaskApi.updateTask(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    planId: undefined,
    elderId: undefined,
    name: undefined,
    categoryId: undefined,
    taskType: undefined,
    status: undefined,
    isRecurring: undefined,
    repeatPattern: undefined,
    startTime: undefined,
    endTime: undefined,
    recommendedExecutors: undefined,
    priority: undefined,
    description: undefined,
  }
  selectedExecutors.value = []
  formRef.value?.resetFields()
}
</script>

<style scoped>
/* 添加树形选择器样式 */
:deep(.el-tree-select) {
  width: 100%;
}
</style>
