<template>
  <Dialog :title="'查看老人档案'" v-model="dialogVisible" width="800px">
    <div class="detail-container" v-loading="detailLoading">
      <!-- 基本信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="avatar-section">
                <div class="avatar-title">头像照片</div>
                <div class="avatar-wrapper">
                  <img v-if="detailData.avatar" :src="detailData.avatar" class="avatar" />
                  <el-icon v-else><UserFilled /></el-icon>
                </div>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="info-list">
                <div class="info-item">
                  <span class="label">姓名：</span>
                  <span class="value">{{ detailData.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">身份证号：</span>
                  <span class="value">{{ detailData.idNumber }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 详细信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>详细信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">性别：</span>
                <span class="value">
                  <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="detailData.gender || 0" />
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">出生日期：</span>
                <span class="value">{{ detailData.birthDate || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">文化程度：</span>
                <span class="value">
                  <dict-tag :type="DICT_TYPE.EDUCATION_LEVEL" :value="detailData.educationLevel || 0" />
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">婚姻状况：</span>
                <span class="value">
                  <dict-tag :type="DICT_TYPE.MARITAL_STATUS" :value="detailData.maritalStatus || 0" />
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">户籍类型：</span>
                <span class="value">
                  <dict-tag :type="DICT_TYPE.HOUSEHOLD_TYPE" :value="detailData.householdType || 0" />
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">居住类型：</span>
                <span class="value">
                  <dict-tag :type="DICT_TYPE.RESIDENCE_TYPE" :value="detailData.residenceType || 0" />
                </span>
              </div>
            </el-col>
          </el-row>
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ detailData.contactPhone || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">服务等级：</span>
            <span class="value">
              <dict-tag :type="DICT_TYPE.ELDER_SERVICE_LEVEL" :value="detailData.serviceLevel || 0" />
            </span>
          </div>
        </div>
      </div>

      <!-- 备注信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>备注信息</span>
        </div>
        <div class="card-content">
          <div class="info-item">
            <span class="label">备注：</span>
            <span class="value">{{ detailData.remark || '暂无备注' }}</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { User, UserFilled, InfoFilled, Document } from '@element-plus/icons-vue'

/** 老人基础信息 详情 */
defineOptions({ name: 'ArchivesProfileDetail' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 详情的加载中
const detailData = ref({
  id: undefined,
  name: '',
  idNumber: '',
  gender: 0,
  birthDate: '',
  contactPhone: '',
  avatar: '',
  serviceLevel: 0,
  educationLevel: 0,
  maritalStatus: 0,
  householdType: 0,
  residenceType: 0,
  remark: ''
})

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    const data = await ArchivesProfileApi.getArchivesProfile(id)
    // 转换日期格式
    if (data.birthDate && Array.isArray(data.birthDate)) {
      data.birthDate = `${data.birthDate[0]}-${String(data.birthDate[1]).padStart(2, '0')}-${String(data.birthDate[2]).padStart(2, '0')}`
    }
    detailData.value = data
  } catch (error: any) {
    message.error(error.message || '获取详情失败')
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20px;
  background-color: var(--el-bg-color);
}

.detail-card {
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);

  .el-icon {
    font-size: 18px;
    color: var(--el-color-primary);
  }
}

.card-content {
  padding: 20px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.avatar-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.avatar-wrapper {
  width: 140px;
  height: 140px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-fill-color-lighter);

  .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .el-icon {
    font-size: 64px;
    color: var(--el-text-color-secondary);
  }
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;

  .label {
    width: 100px;
    color: var(--el-text-color-secondary);
    font-weight: 500;
  }

  .value {
    flex: 1;
    color: var(--el-text-color-primary);
  }
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid var(--el-border-color-lighter);
}
</style> 