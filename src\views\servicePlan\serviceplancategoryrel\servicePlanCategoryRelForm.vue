<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="评估模板" prop="templateId">
        <el-select 
          v-model="formData.templateId" 
          placeholder="请选择评估模板"
          style="width: 100%;"
          filterable
        >
          <el-option
            v-for="template in templateList"
            :key="template.id"
            :label="template.name"
            :value="template.id"
          >
            <span>{{ template.name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="有效期天数" prop="validityDays">
        <el-input-number 
          v-model="formData.validityDays" 
          :min="1" 
          :max="365"
          placeholder="请输入有效期天数" 
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="isActive">
        <el-radio-group v-model="formData.isActive">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.PLAN_CATEGORY_REL_ACTIVE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>


      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PLAN_CATEGORY_REL_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { servicePlanCategoryRelApi, servicePlanCategoryRelVO } from '@/api/servicePlan/serviceplancategoryrel'
import { DICT_TYPE , getIntDictOptions } from '@/utils/dict'
import { TemplateApi } from '@/api/evaluation/template'

/** 计划分类评估关联表 表单 */
defineOptions({ name: 'servicePlanCategoryRelForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 从父组件接收分类ID
const props = defineProps({
  categoryId: {
    type: Number,
    default: undefined
  }
})

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const templateList = ref([]); // 评估模板列表

const formData = ref({
  id: undefined,
  categoryId: undefined,
  templateId: undefined,
  validityDays: 30, // 默认30天
  isActive: 1, // 默认启用
  priority: 4, // 默认优先级
})
const formRules = reactive({
  templateId: [{ required: true, message: '评估模板不能为空', trigger: 'change' }],
  validityDays: [{ required: true, message: '有效期天数不能为空', trigger: 'blur' }],
  isActive: [{ required: true, message: '是否启用不能为空', trigger: 'blur' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

// 获取评估模板列表
const getTemplateList = async () => {
  try {
    templateList.value = await TemplateApi.getSimpleTemplateList()
  } catch (error) {
    console.error('获取评估模板列表失败:', error)
    message.error('获取评估模板列表失败')
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 获取评估模板列表
  await getTemplateList()
  
  // 设置分类ID
  if (props.categoryId) {
    formData.value.categoryId = props.categoryId
  }
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await servicePlanCategoryRelApi.getservicePlanCategoryRel(id)
      formData.value.isActive = Number(formData.value.isActive)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 确保分类ID已设置
  if (!formData.value.categoryId) {
    message.error('分类ID未设置')
    return
  }
  
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as servicePlanCategoryRelVO
    if (formType.value === 'create') {
      await servicePlanCategoryRelApi.createservicePlanCategoryRel(data)
      message.success(t('common.createSuccess'))
    } else {
      await servicePlanCategoryRelApi.updateservicePlanCategoryRel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    categoryId: props.categoryId, // 使用props中的分类ID
    templateId: undefined,
    validityDays: 30, // 默认值
    isActive: 1, // 默认启用
    priority: 4, // 默认优先级
  }
  formRef.value?.resetFields()
}
</script>