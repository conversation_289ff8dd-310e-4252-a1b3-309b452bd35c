<template>
  <div class="service-plan-category">
    <div class="mb-4">
      <el-button type="primary" @click="openForm('create')">
        新增分类
      </el-button>
    </div>

    <el-tree
      v-loading="loading"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      default-expand-all
    >
      <template #default="{ data }">
        <div class="custom-card">
          <div class="card-header">
            <div class="header-left">
              <span :class="[
                'name-text',
                data.level === 1 ? 'text-20 font-medium' : 'text-16'
              ]">
                {{ data.name }}
              </span>
              <span class="text-gray-400 text-sm ml-2">
                {{ data.children?.length > 0 ? `(${data.children?.length}个子分类)` : '' }}
              </span>
            </div>
            <div class="header-right">
              <el-button
                link
                type="primary"
                size="default"
                @click.stop="openForm('create', undefined, data)"
              >
                添加子分类
              </el-button>
              <el-button
                link
                type="primary"
                size="default"
                @click.stop="openForm('update', data.id)"
              >
                编辑
              </el-button>
              <el-button
                v-if="data.children?.length === 0"
                link
                type="success"
                size="default"
                @click.stop="handleLinkAssessment(data)"
              >
                关联评估表
              </el-button>
              <el-button
                link
                type="danger"
                size="default"
                @click.stop="handleDelete(data)"
                :disabled="data.children?.length > 0"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-tree>

    <!-- 引入 CategoryForm 组件 -->
    <category-form
      ref="formRef"
      @success="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CategoryApi } from '@/api/servicePlan/serviceplancategory'
import CategoryForm from './CategoryForm.vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ServicePlanCategory',
})

interface CategoryForm {
  id?: number
  name: string
  description?: string
  status?: number
  children?: CategoryForm[]
  parentId?: number | null
  ancestors?: string
  level?: number
  difyKey?: string | null
}

const router = useRouter()
// 树形数据
const treeData = ref<CategoryForm[]>([])
const loading = ref(false)
const formRef = ref()

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'name',
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await CategoryApi.generateCategoryTree()
    treeData.value = res || []
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
    treeData.value = []
  } finally {
    loading.value = false
  }
}

// 打开表单
const openForm = (type: string, id?: number, parentNode?: CategoryForm) => {
  if (type === 'create' && parentNode) {
    // 如果是添加子分类，需要设置父级信息
    formRef.value?.open(type, undefined, {
      parentId: parentNode.id,
      level: (parentNode.level || 0) + 1,
      ancestors: parentNode.ancestors ? `${parentNode.ancestors},${parentNode.id}` : parentNode.id?.toString(),
    })
  } else {
    formRef.value?.open(type, id)
  }
}

// 删除分类
const handleDelete = async (row: CategoryForm) => {
  if (row.children?.length) {
    ElMessage.warning('请先删除子分类')
    return
  }
  try {
    await ElMessageBox.confirm('确认删除该分类吗？', '提示', {
      type: 'warning',
    })
    await CategoryApi.deleteCategory(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    console.error(error)
  }
}

// 处理关联评估表
const handleLinkAssessment = (row: CategoryForm) => {
  // 在这里添加关联评估表的逻辑
  console.log('关联评估表', row)
  // 可以跳转到关联页面或打开关联弹窗
  router.push({
    path: '/servicePlan/category-rel',
    query: {
      categoryId: row.id,
      categoryName: row.name,
    },
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.service-plan-category {
  padding: 20px;
}

/* 调整树形结构布局 */
:deep(.el-tree) {
  --el-tree-node-hover-bg-color: transparent; /* 移除悬停背景色 */
  background: transparent;
}

/* 树节点容器 */
:deep(.el-tree-node__content) {
  height: auto; /* 允许高度自适应 */
  padding: 4px 0; /* 增加垂直间距 */
  position: relative;
}

/* 自定义卡片布局 */
.custom-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  width: 100%; /* 改为100%宽度 */
  box-sizing: border-box;
  position: relative;
  margin-left: 0; /* 移除左侧margin */
  transition: all 0.3s;
}

/* 添加连接线样式 */
:deep(.el-tree-node__children) {
  position: relative;
  padding-left: 24px; /* 为连接线留出空间 */
}

:deep(.el-tree-node__children::before) {
  content: "";
  position: absolute;
  left: 12px;
  top: -20px;
  width: 1px;
  height: calc(100% + 20px);
  background-color: #e4e7ed;
}

:deep(.el-tree-node:last-child > .el-tree-node__content) {
  padding-bottom: 0;
}

/* 适配多级缩进 */
:deep(.el-tree-node) {
  position: static; /* 修复绝对定位导致的布局问题 */
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.name-text {
  color: var(--el-text-color-primary);
  transition: color 0.3s;
}

.text-20 {
  font-size: 20px;
}

.text-16 {
  font-size: 16px;
}

.flex-grow {
  flex-grow: 1;
}

.gap-2 {
  gap: 0.5rem;
}
</style>
