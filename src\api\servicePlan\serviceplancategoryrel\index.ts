import request from '@/config/axios'

// 计划分类评估关联表 VO
export interface servicePlanCategoryRelVO {
  id: number // 配置ID
  categoryId: number // 服务计划分类ID
  templateId: number // 评估模板ID
  validityDays: number // 有效期天数
  isActive: boolean // 是否启用
  priority: number // 优先级
}

// 计划分类评估关联表 API
export const servicePlanCategoryRelApi = {
  // 查询计划分类评估关联表详情
  getservicePlanCategoryRel: async (id: number) => {
    return await request.get({ url: `/servicePlan/service-plan-category-rel/get?id=` + id })
  },

  // 根据分类ID获取关联列表
  listByCategoryId: async (categoryId: number) => {
    return await request.get({ url: `/servicePlan/service-plan-category-rel/list-by-category?categoryId=${categoryId}` })
  },

  // 新增计划分类评估关联表
  createservicePlanCategoryRel: async (data: servicePlanCategoryRelVO) => {
    return await request.post({ url: `/servicePlan/service-plan-category-rel/create`, data })
  },

  // 修改计划分类评估关联表
  updateservicePlanCategoryRel: async (data: servicePlanCategoryRelVO) => {
    return await request.put({ url: `/servicePlan/service-plan-category-rel/update`, data })
  },

  // 删除计划分类评估关联表
  deleteservicePlanCategoryRel: async (id: number) => {
    return await request.delete({ url: `/servicePlan/service-plan-category-rel/delete?id=` + id })
  },

  // 获取激活的关联列表
  getActiveList: async (categoryId: number, elderId: number) => {
    return await request.get({ url: `/servicePlan/service-plan-category-rel/active-list?categoryId=${categoryId}&elderId=${elderId}` })
  }


}
