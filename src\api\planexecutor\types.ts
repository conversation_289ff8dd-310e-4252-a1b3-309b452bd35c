export interface PlanExecutorVO {
  id?: number
  taskId?: number
  executorId?: number
  elderId?: number
  planId?: number
  executeDate?: string // 添加执行日期字段
  startTime?: string
  endTime?: string
  status?: number
  actualStart?: number
  actualEnd?: number
}

export interface CheckConflictReq {
  id?: number
  taskId?: number // 添加 taskId 字段
  elderId?: number
  executorId?: number
  dates: string[]
  startTime: string
  endTime: string
} 