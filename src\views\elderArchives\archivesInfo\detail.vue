<template>
  <ContentWrap>
    <!-- 基本信息卡片 -->
    <el-card class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="font-medium">基本信息</span>
        </div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="姓名">{{ elderInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="性别">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="elderInfo.gender" />
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ elderInfo.idNumber }}</el-descriptions-item>
        <el-descriptions-item label="出生日期">{{ formatDate(elderInfo.birthDate) }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ elderInfo.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="服务等级">
          <dict-tag :type="DICT_TYPE.ELDER_SERVICE_LEVEL" :value="elderInfo.serviceLevel" />
        </el-descriptions-item>
        <el-descriptions-item label="文化程度">
          <dict-tag :type="DICT_TYPE.EDUCATION_LEVEL" :value="elderInfo.educationLevel" />
        </el-descriptions-item>
        <el-descriptions-item label="婚姻状况">
          <dict-tag :type="DICT_TYPE.MARITAL_STATUS" :value="elderInfo.maritalStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="户籍类型">
          <dict-tag :type="DICT_TYPE.HOUSEHOLD_TYPE" :value="elderInfo.householdType" />
        </el-descriptions-item>
        <el-descriptions-item label="居住类型">
          <dict-tag :type="DICT_TYPE.RESIDENCE_TYPE" :value="elderInfo.residenceType" />
        </el-descriptions-item>
        <el-descriptions-item label="医疗保险" :span="2">
          <template v-if="elderInfo.medicalInsurance">
            <dict-tag v-for="item in elderInfo.medicalInsurance.split(',')" 
                     :key="item" 
                     :type="DICT_TYPE.MEDICAL_INSURANCE" 
                     :value="parseInt(item)" />
          </template>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- Tab切换 -->
    <el-card class="tab-card">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="详细信息" name="detail">
          <template #label>
            <div class="flex items-center">
              <Icon icon="ep:document" class="mr-5px" />
              详细信息
            </div>
          </template>
          <!-- 详细信息内容 -->
          <div class="detail-content">
            <div class="flex justify-end">
              <el-button type="primary" @click="handleEdit" v-hasPermi="['elderArchives:archives-extension:update']">
                <Icon icon="ep:edit" class="mr-5px" />编辑
              </el-button>
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="自理能力">
                <dict-tag :type="DICT_TYPE.ELDER_SELF_CARE_ABILITY" :value="extendInfo.selfCareAbility" />
              </el-descriptions-item>
              <el-descriptions-item label="精神状态">
                <dict-tag :type="DICT_TYPE.ELDER_MENTAL_STATE" :value="extendInfo.mentalState" />
              </el-descriptions-item>
              <el-descriptions-item label="躯体疾病">
                <template v-if="extendInfo.physicalDisease">
                  <dict-tag v-for="item in extendInfo.physicalDisease.split(',')" 
                            :key="item" 
                            :type="DICT_TYPE.PHYSICAL_DISEASE" 
                            :value="Number(item)" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="精神疾病">
                <template v-if="extendInfo.mentalDisease">
                  <dict-tag v-for="item in extendInfo.mentalDisease.split(',')" 
                            :key="item" 
                            :type="DICT_TYPE.MENTAL_DISEASE" 
                            :value="Number(item)" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="服药情况">{{ extendInfo.medicationStatus }}</el-descriptions-item>
              <el-descriptions-item label="跌倒史">
                <dict-tag :type="DICT_TYPE.FALL_HISTORY" :value="extendInfo.fallHistory" />
              </el-descriptions-item>
              <el-descriptions-item label="走失史">
                <dict-tag :type="DICT_TYPE.WANDERING_HISTORY" :value="extendInfo.wanderingHistory" />
              </el-descriptions-item>
              <el-descriptions-item label="住院史">
                <dict-tag :type="DICT_TYPE.HOSPITALIZATION_HISTORY" :value="extendInfo.hospitalizationHistory" />
              </el-descriptions-item>
              <el-descriptions-item label="其他意外事件">{{ extendInfo.otherAccidents }}</el-descriptions-item>
              <el-descriptions-item label="记录日期">{{ formatDate(extendInfo.recordDate) }}</el-descriptions-item>
              <el-descriptions-item label="记录人">{{ extendInfo.recorder }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ extendInfo.remark }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="历史记录" name="history">
          <template #label>
            <div class="flex items-center">
              <Icon icon="ep:time" class="mr-5px" />
              历史记录
            </div>
          </template>
          <!-- 历史记录内容 -->
          <div class="history-content">
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in historyList"
                :key="index"
                :timestamp="formatDate(history.recordDate)"
                placement="top"
              >
                <el-card>
                  <h4>记录人：{{ history.recorder }}</h4>
                  <el-descriptions :column="2" border size="small" class="mt-4">
                    <el-descriptions-item label="自理能力">
                      <dict-tag :type="DICT_TYPE.ELDER_SELF_CARE_ABILITY" :value="history.selfCareAbility" />
                    </el-descriptions-item>
                    <el-descriptions-item label="精神状态">
                      <dict-tag :type="DICT_TYPE.ELDER_MENTAL_STATE" :value="history.mentalState" />
                    </el-descriptions-item>
                    <el-descriptions-item label="躯体疾病">
                      <template v-if="history.physicalDisease">
                        <dict-tag v-for="item in history.physicalDisease.split(',')" 
                                  :key="item" 
                                  :type="DICT_TYPE.PHYSICAL_DISEASE" 
                                  :value="Number(item)" />
                      </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="精神疾病">
                      <template v-if="history.mentalDisease">
                        <dict-tag v-for="item in history.mentalDisease.split(',')" 
                                  :key="item" 
                                  :type="DICT_TYPE.MENTAL_DISEASE" 
                                  :value="Number(item)" />
                      </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="服药情况">{{ history.medicationStatus }}</el-descriptions-item>
                    <el-descriptions-item label="跌倒史">
                      <dict-tag :type="DICT_TYPE.FALL_HISTORY" :value="history.fallHistory" />
                    </el-descriptions-item>
                    <el-descriptions-item label="走失史">
                      <dict-tag :type="DICT_TYPE.WANDERING_HISTORY" :value="history.wanderingHistory" />
                    </el-descriptions-item>
                    <el-descriptions-item label="住院史">
                      <dict-tag :type="DICT_TYPE.HOSPITALIZATION_HISTORY" :value="history.hospitalizationHistory" />
                    </el-descriptions-item>
                    <el-descriptions-item label="其他意外事件">{{ history.otherAccidents }}</el-descriptions-item>
                  </el-descriptions>
                  <p class="text-gray-500 text-sm mt-2">备注：{{ history.remark || '无' }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            
            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="historyPagination.pageNo"
                v-model:page-size="historyPagination.pageSize"
                :total="historyPagination.total"
                :page-sizes="[10, 20, 50, 100]"
                small
                background
                layout="total, sizes, prev, pager, next"
                @size-change="handleHistorySizeChange"
                @current-change="handleHistoryPageChange"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 编辑弹窗 -->
    <ExtendForm ref="formRef" @submit="handleSubmit" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ArchivesExtensionApi } from '@/api/elderArchives/archivesExtension'
import type { ArchivesExtensionVO } from '@/api/elderArchives/archivesExtension'
import ExtendForm from './components/ExtendForm.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 获取路由参数
const elderId = route.query.elder_id as string

interface ElderInfo {
  id: number
  name: string
  gender: number
  idNumber: string
  birthDate: Date
  contactPhone: string
  serviceLevel: number
  educationLevel: number
  maritalStatus: number
  householdType: number
  residenceType: string
  medicalInsurance: string
}

// 基本信息
const elderInfo = ref<ElderInfo>({} as ElderInfo)
// 拓展信息
const extendInfo = ref<ArchivesExtensionVO>({} as ArchivesExtensionVO)
// 历史记录
const historyList = ref<ArchivesExtensionVO[]>([])
// 历史记录分页
const historyPagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})
// 当前激活的tab
const activeTab = ref('detail')

// 编辑表单的ref
const formRef = ref()

/** 获取老人基本信息 */
const getElderInfo = async () => {
  try {
    const data = await ArchivesProfileApi.getArchivesProfile(parseInt(elderId))
    elderInfo.value = {
      ...data,
      birthDate: new Date(data.birthDate)
    }
  } catch (error) {
    message.error('获取基本信息失败')
  }
}

/** 获取拓展信息 */
const getExtendInfo = async () => {
  try {
    // 获取最新的一条记录
    const data = await ArchivesExtensionApi.getArchivesExtensionPage({
      elderId: parseInt(elderId),
      pageNo: 1,
      pageSize: 1
    })
    console.log('获取拓展信息返回数据:', data)
    
    if (data && data.list && data.list.length > 0) {
      const info = data.list[0]
      // 处理数字类型字段
      info.selfCareAbility = Number(info.selfCareAbility) || null
      info.mentalState = Number(info.mentalState) || null
      info.fallHistory = Number(info.fallHistory) || null
      info.wanderingHistory = Number(info.wanderingHistory) || null
      info.hospitalizationHistory = Number(info.hospitalizationHistory) || null
      
      extendInfo.value = info
      console.log('当前拓展信息:', extendInfo.value)
    } else {
      console.warn('未找到拓展信息')
      extendInfo.value = {} as ArchivesExtensionVO
    }
  } catch (error: unknown) {
    console.error('获取拓展信息失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    message.error('获取拓展信息失败：' + errorMessage)
  }
}

/** 获取历史记录 */
const getHistoryList = async () => {
  if (!elderId) {
    message.error('老人ID不能为空')
    return
  }
  
  try {
    const params = {
      elderId: parseInt(elderId),
      pageNo: historyPagination.pageNo,
      pageSize: historyPagination.pageSize
    }
    console.log('查询历史记录参数:', params)
    
    const data = await ArchivesExtensionApi.getArchivesExtensionPage(params)
    console.log('历史记录返回数据:', data)
    
    if (data && data.list) {
      historyList.value = data.list
      historyPagination.total = data.total
    } else {
      historyList.value = []
      historyPagination.total = 0
      console.warn('历史记录数据为空')
    }
  } catch (error: any) {
    console.error('获取历史记录失败:', error)
    message.error('获取历史记录失败：' + (error.message || '未知错误'))
  }
}

/** 编辑按钮操作 */
const handleEdit = () => {
  // 打开编辑弹窗
  openEditForm()
}

/** 打开编辑弹窗 */
const openEditForm = () => {
  // 基于最新的拓展信息创建新记录
  const formData = {
    elderId: parseInt(elderId),
    selfCareAbility: Number(extendInfo.value.selfCareAbility) || null,
    mentalState: Number(extendInfo.value.mentalState) || null,
    physicalDisease: extendInfo.value.physicalDisease ? extendInfo.value.physicalDisease.split(',').map(Number) : [],
    mentalDisease: extendInfo.value.mentalDisease ? extendInfo.value.mentalDisease.split(',').map(Number) : [],
    medicationStatus: extendInfo.value.medicationStatus || '',
    fallHistory: Number(extendInfo.value.fallHistory) || null,
    wanderingHistory: Number(extendInfo.value.wanderingHistory) || null,
    hospitalizationHistory: Number(extendInfo.value.hospitalizationHistory) || null,
    otherAccidents: extendInfo.value.otherAccidents || '',
    remark: extendInfo.value.remark || ''
  }
  
  formRef.value?.open(formData)
}

/** 处理历史记录分页变化 */
const handleHistoryPageChange = (page: number) => {
  historyPagination.pageNo = page
  getHistoryList()
}

/** 处理历史记录每页条数变化 */
const handleHistorySizeChange = (size: number) => {
  historyPagination.pageSize = size
  historyPagination.pageNo = 1
  getHistoryList()
}

/** 提交表单 */
const handleSubmit = async (formData: any) => {
  try {
    // 保存拓展信息
    await ArchivesExtensionApi.createArchivesExtension({
      ...formData,
      elderId: parseInt(elderId),
      // 将数组转换为字符串
      physicalDisease: formData.physicalDisease.map(String),
      mentalDisease: formData.mentalDisease.map(String)
    })
    
    message.success('保存成功')
    
    // 刷新拓展信息和历史记录
    await getExtendInfo()
    await getHistoryList()
  } catch (error: unknown) {
    console.error('保存失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    message.error('保存失败：' + errorMessage)
  }
}

/** 初始化 */
onMounted(async () => {
  if (!elderId) {
    message.error('参数错误')
    router.push('/elder-archives/archives-info')
    return
  }
  
  await getElderInfo()
  await getExtendInfo()
  await getHistoryList()
})
</script>

<style scoped>
.detail-content {
  padding: 0;  /* 移除内边距 */
}

:deep(.el-descriptions) {
  padding: 0;  /* 移除描述列表的内边距 */
}

:deep(.el-card__body) {
  padding: 16px;  /* 统一卡片内边距 */
}

:deep(.el-timeline) {
  padding: 8px 16px;  /* 调整时间线的内边距 */
}

:deep(.el-timeline-item__content) {
  min-width: 300px;
}

:deep(.el-timeline-item__node) {
  margin-top: 4px;  /* 调整时间线节点位置 */
}

:deep(.el-descriptions__body) {
  background-color: #fff;  /* 确保背景色统一 */
}

:deep(.el-descriptions__label) {
  width: 120px;  /* 统一标签宽度 */
  color: #606266;  /* 标签文字颜色 */
}

:deep(.el-descriptions__content) {
  padding: 12px 16px;  /* 内容区域内边距 */
}

:deep(.el-card) {
  margin-bottom: 16px;  /* 卡片之间的间距 */
}

/* 历史记录卡片样式 */
:deep(.el-timeline-item .el-card) {
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 历史记录标题样式 */
:deep(.el-timeline-item .el-card h4) {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

/* 分页器样式 */
:deep(.el-pagination) {
  padding: 16px 0;  /* 分页器上下间距 */
  margin-top: 16px;  /* 与上方内容的间距 */
  background-color: #fff;
}

/* Tab 内容区域样式 */
:deep(.el-tab-pane) {
  padding: 16px 0;  /* Tab 内容区域内边距 */
}

/* 编辑按钮容器样式 */
.flex.justify-end {
  margin-bottom: 16px;  /* 编辑按钮与下方内容的间距 */
}

/* 标签组样式 */
:deep(.dict-tag) {
  margin-right: 8px;  /* 标签之间的间距 */
  margin-bottom: 4px;  /* 标签换行时的间距 */
}
</style> 