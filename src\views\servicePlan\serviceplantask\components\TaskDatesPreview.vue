<template>
  <div class="task-dates-preview preview-container">
    <div class="preview-header mb-2">
      <h3 class="text-lg font-medium">任务日期预览</h3>
      <el-form-item label="" prop="dates">
        <el-date-picker
          :model-value="props.generatedDates"
          @update:model-value="updateDates"
          type="dates"
          placeholder="选择一个或多个日期"
          value-format="YYYY-MM-DD"
          popper-class="date-picker-readonly"
          style="width: 360px;"
          @mousedown.prevent
          readonly
          disabled
        />
      </el-form-item>
    </div>
    <div class="text-gray-500 text-sm mt-2">
      <p>共 {{ props.generatedDates.length }} 个任务日期</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 定义 props
const props = defineProps({
  generatedDates: {
    type: Array,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['update:generatedDates'])

// 更新日期的方法
const updateDates = (newDates) => {
  emit('update:generatedDates', newDates)
}
</script>

<style scoped>
.preview-container {
  width: 100%;
  padding-left: 20px;
}

.text-lg {
  font-size: 18px;
}

.text-gray-500 {
  color: #666;
}

.font-medium {
  font-weight: 500;
}
</style> 