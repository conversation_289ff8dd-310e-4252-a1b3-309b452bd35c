import request from '@/config/axios'

export interface ArchivesCombinedVO {
  id: number
  name: string
  sex: number
  idCard: string
  birthday: string
  phone: string
  address: string
  emergencyContactName: string
  emergencyContactPhone: string
  healthCondition: string
  selfCareAbility: number
  economicSource: number
  livingCondition: number
  recorder: string
  recordDate: string
}

export interface ArchivesCombinedPageReqVO {
  pageNo: number
  pageSize: number
  name?: string
  idCard?: string
  phone?: string
  address?: string
}

export interface ArchivesCombinedPageVO {
  list: ArchivesCombinedVO[]
  total: number
}

export const ArchivesCombinedApi = {
  // 获得老人综合信息分页
  getArchivesCombinedPage(params: ArchivesCombinedPageReqVO) {
    return request.get({ url: '/elderArchives/archives-combined/page', params })
  },

  // 导出老人综合信息 Excel
  exportArchivesCombined(params: ArchivesCombinedPageReqVO) {
    return request.download({ url: '/elderArchives/archives-combined/export-excel', params })
  }
} 