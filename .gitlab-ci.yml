variables:
  # 目录配置
  FRONT_BUILD_PATH: /data/ruoyi/front/build
  FRONT_DIST_PATH: /data/ruoyi/front/dist
  PROJECT_NAME: ruoyi-ui
  DOCKER_HOST: unix:///var/run/docker.sock
  DOCKER_TLS_CERTDIR: ""

default:
  tags:
    - docker
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

stages:
  - prepare
  - build
  - deploy

.setup_colors: &setup_colors
  - |
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    NC='\033[0m' # No Color
    echo "正在设置颜色变量..."

prepare:
  stage: prepare
  image: docker:latest
  before_script:
    - *setup_colors
    - echo -e "${YELLOW}=== 开始准备阶段 ===${NC}"
  script:
    - echo -e "${YELLOW}创建必要的目录...${NC}"
    - mkdir -p $FRONT_BUILD_PATH
    - mkdir -p $FRONT_DIST_PATH
    - echo -e "${YELLOW}设置目录权限...${NC}"
    - chmod -R 777 $FRONT_BUILD_PATH
    - chmod -R 777 $FRONT_DIST_PATH
    - echo -e "${GREEN}✓ 准备阶段完成${NC}"
  after_script:
    - echo -e "${YELLOW}=== 准备阶段结束 ===${NC}"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always

# 使用缓存加速构建
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/
    - .pnpm-store/

build:
  stage: build
  image: node:18
  before_script:
    - *setup_colors
    - echo -e "${YELLOW}=== 开始构建阶段 ===${NC}"
  script:
    - echo -e "${YELLOW}安装 pnpm...${NC}"
    - npm install -g pnpm
    - echo -e "${YELLOW}安装项目依赖...${NC}"
    - pnpm install --registry=https://registry.npm.taobao.org
    - echo -e "${GREEN}✓ 依赖安装完成${NC}"
    - echo -e "${YELLOW}开始项目构建...${NC}"
    - npm run build:prod
    - echo -e "${GREEN}✓ 构建完成${NC}"
    - echo -e "${YELLOW}检查构建产物...${NC}"
    - |
      if [ ! -d "dist-prod" ]; then
        echo -e "${RED}错误: 构建目录不存在${NC}"
        exit 1
      fi
    - echo -e "${YELLOW}复制构建文件...${NC}"
    - cp -r dist-prod/* $FRONT_BUILD_PATH/
    - cp nginx.conf $FRONT_BUILD_PATH/
    - cp -r $FRONT_BUILD_PATH/* $FRONT_DIST_PATH/
    - echo -e "${GREEN}✓ 文件复制完成${NC}"
  after_script:
    - echo -e "${YELLOW}=== 构建阶段结束 ===${NC}"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
  needs:
    - prepare

deploy:
  stage: deploy
  image: docker:latest
  before_script:
    - *setup_colors
    - echo -e "${YELLOW}=== 开始部署阶段 ===${NC}"
  script:
    - echo -e "${YELLOW}检查部署文件...${NC}"
    - ls -la $FRONT_DIST_PATH
    - |
      if [ ! -f "$FRONT_DIST_PATH/nginx.conf" ]; then
        echo -e "${RED}错误: nginx配置文件不存在${NC}"
        exit 1
      fi
    - echo -e "${YELLOW}清理旧容器...${NC}"
    - docker stop ${PROJECT_NAME} || true
    - docker rm ${PROJECT_NAME} || true
    - echo -e "${YELLOW}启动新容器...${NC}"
    - |
      docker run -d \
        --name ${PROJECT_NAME} \
        -p 80:80 \
        -v $FRONT_DIST_PATH:/usr/share/nginx/html \
        -v $FRONT_DIST_PATH/nginx.conf:/etc/nginx/conf.d/default.conf:ro \
        --restart always \
        nginx:stable
    - echo -e "${YELLOW}验证容器状态...${NC}"
    - |
      if ! docker ps | grep ${PROJECT_NAME}; then
        echo -e "${RED}错误: 容器未正常运行${NC}"
        exit 1
      fi
    - echo -e "${GREEN}✓ 容器已成功启动${NC}"
  after_script:
    - echo -e "${YELLOW}=== 部署阶段结束 ===${NC}"
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
  needs:
    - build
  variables:
    GIT_STRATEGY: none