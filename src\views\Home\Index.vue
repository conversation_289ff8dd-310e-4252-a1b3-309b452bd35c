<template>
  <div>
    <el-card shadow="never">
      <el-skeleton :loading="loading" animated>
        <el-row :gutter="16" justify="space-between">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="flex items-center">
              <el-avatar :src="avatar" :size="70" class="mr-16px">
                <img src="@/assets/imgs/avatar.gif" alt="" />
              </el-avatar>
              <div>
                <div class="text-20px">
                  {{ t('workplace.welcome') }} {{ username }} {{ t('workplace.happyDay') }}
                </div>
                <div class="mt-10px text-14px text-gray-500">
                  今日天气：{{ weather.condition }}，{{ weather.temperature }}℃
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <div class="h-70px flex items-center justify-end lt-sm:mt-10px">
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">入住老人</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.elderly"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" />
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">今日任务</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.todo"
                  :duration="2600"
                />
              </div>
              <el-divider direction="vertical" border-style="dashed" />
              <div class="px-8px text-right">
                <div class="mb-16px text-14px text-gray-400">护理记录</div>
                <CountTo
                  class="text-20px"
                  :start-val="0"
                  :end-val="totalSate.careRecords"
                  :duration="2600"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-skeleton>
    </el-card>
  </div>

  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>服务模块</span>
            <el-link
              type="primary"
              :underline="false"
            >
              {{ t('action.more') }}
            </el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col
              v-for="(item, index) in serviceModules"
              :key="`card-${index}`"
              :xl="8"
              :lg="8"
              :md="8"
              :sm="24"
              :xs="24"
            >
              <el-card
                shadow="hover"
                class="mr-5px mt-5px cursor-pointer"
                @click="handleModuleClick(item.url)"
              >
                <div class="flex items-center">
                  <Icon
                    :icon="item.icon"
                    :size="25"
                    class="mr-8px"
                    :style="{ color: item.color }"
                  />
                  <span class="text-16px">{{ item.name }}</span>
                </div>
                <div class="mt-12px text-12px text-gray-400">{{ item.description }}</div>
                <div class="mt-12px flex justify-between text-12px text-gray-400">
                  <span>{{ item.status }}</span>
                  <span>{{ item.count }}条记录</span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>

      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>数据分析</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row :gutter="20" justify="space-between">
            <el-col :xl="10" :lg="10" :md="24" :sm="24" :xs="24">
              <el-card shadow="hover" class="mb-8px">
                <div class="text-center mb-8px text-16px">老人健康状况分布</div>
                <el-skeleton :loading="loading" animated>
                  <Echart :options="pieOptionsData" :height="280" />
                </el-skeleton>
              </el-card>
            </el-col>
            <el-col :xl="14" :lg="14" :md="24" :sm="24" :xs="24">
              <el-card shadow="hover" class="mb-8px">
                <div class="text-center mb-8px text-16px">近7天护理服务统计</div>
                <el-skeleton :loading="loading" animated>
                  <Echart :options="barOptionsData" :height="280" />
                </el-skeleton>
              </el-card>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
    </el-col>
    <el-col :xl="8" :lg="8" :md="24" :sm="24" :xs="24" class="mb-8px">
      <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>快捷入口</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col v-for="item in shortcut" :key="`team-${item.name}`" :span="8" class="mb-8px">
              <div class="flex items-center">
                <Icon :icon="item.icon" class="mr-8px" :style="{ color: item.color }" />
                <el-link type="default" :underline="false" @click="handleShortcutClick(item.url)">
                  {{ item.name }}
                </el-link>
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>

      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>今日提醒</span>
            <el-link type="primary" :underline="false">{{ t('action.more') }}</el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-for="(item, index) in reminders" :key="`reminder-${index}`">
            <div class="flex items-center">
              <el-tag :type="item.tagType" class="mr-16px" size="small" effect="dark">
                {{ item.priority }}
              </el-tag>
              <div>
                <div class="text-14px">
                  <Highlight :keys="item.keys">
                    {{ item.title }}
                  </Highlight>
                </div>
                <div class="mt-8px text-12px text-gray-400">
                  {{ formatTime(item.time, 'HH:mm') }} | {{ item.location }}
                </div>
              </div>
            </div>
            <el-divider />
          </div>
        </el-skeleton>
      </el-card>

      <el-card shadow="never" class="mt-8px">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>通知公告</span>
            <el-link type="primary" :underline="false">{{ t('action.more') }}</el-link>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <div v-for="(item, index) in notice" :key="`notice-${index}`">
            <div class="flex items-center">
              <el-avatar :src="avatar" :size="35" class="mr-16px">
                <img src="@/assets/imgs/avatar.gif" alt="" />
              </el-avatar>
              <div>
                <div class="text-14px">
                  <Highlight :keys="item.keys">
                    {{ item.title }}
                  </Highlight>
                </div>
                <div class="mt-16px text-12px text-gray-400">
                  {{ formatTime(item.date, 'yyyy-MM-dd') }}
                </div>
              </div>
            </div>
            <el-divider />
          </div>
        </el-skeleton>
      </el-card>
    </el-col>
  </el-row>
</template>
<script lang="ts" setup>
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import { formatTime } from '@/utils'

import { useUserStore } from '@/store/modules/user'
// import { useWatermark } from '@/hooks/web/useWatermark'
import type { WorkplaceTotal, Shortcut } from './types'
import { pieOptions, barOptions } from './echarts-data'
import { useRouter } from 'vue-router'

defineOptions({ name: 'Index' })

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
// const { setWatermark } = useWatermark()
const loading = ref(true)
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const pieOptionsData = reactive<EChartsOption>(pieOptions) as EChartsOption
// 天气信息
const weather = reactive({
  condition: '晴',
  temperature: '25'
})

// 获取统计数
let totalSate = reactive<WorkplaceTotal>({
  elderly: 0,
  careRecords: 0,
  todo: 0
})

const getCount = async () => {
  const data = {
    elderly: 128,
    careRecords: 1240,
    todo: 15
  }
  totalSate = Object.assign(totalSate, data)
}

// 获取服务模块
let serviceModules = reactive<any[]>([])
const getServiceModules = async () => {
  const data = [
    {
      name: '老人档案',
      icon: 'mdi:folder-account-outline',
      description: '管理老人的基本信息和健康档案',
      status: '已完成评估',
      count: 128,
      url: 'http://localhost/elders/archivesInfo',
      color: '#409EFF'
    },
    {
      name: '评估系统',
      icon: 'mdi:clipboard-check-outline',
      description: '老人能力评估和健康状况评定',
      status: '本周待评估',
      count: 12,
      url: 'http://localhost/evaluation/result',
      color: '#67C23A'
    },
    {
      name: '服务计划',
      icon: 'mdi:calendar-clock',
      description: '制定个性化的养老服务计划',
      status: '待执行计划',
      count: 35,
      url: 'http://localhost/servicePlan/service-plan',
      color: '#E6A23C'
    },
    {
      name: '护理记录',
      icon: 'mdi:clipboard-text-outline',
      description: '记录日常护理和服务情况',
      status: '今日已完成',
      count: 86,
      url: '/service/record',
      color: '#F56C6C'
    },
    {
      name: '健康监测',
      icon: 'mdi:heart-pulse',
      description: '监测老人生命体征和健康指标',
      status: '异常提醒',
      count: 3,
      url: '/health/monitor',
      color: '#909399'
    },
    {
      name: '活动安排',
      icon: 'mdi:calendar-star',
      description: '安排老人日常活动和娱乐',
      status: '今日活动',
      count: 4,
      url: '/activity/list',
      color: '#9C27B0'
    }
  ]
  serviceModules = Object.assign(serviceModules, data)
}

// 获取通知公告
let notice = reactive<any[]>([])
const getNotice = async () => {
  const data = [
    {
      title: '养老院消防安全培训将于本周五下午2点在多功能厅举行',
      keys: ['消防安全培训', '周五'],
      date: new Date()
    },
    {
      title: '本月老人满意度调查结果已出，总体满意度达95%',
      keys: ['满意度调查', '95%'],
      date: new Date(Date.now() - 24 * 60 * 60 * 1000)
    },
    {
      title: '新版《老年人能力评估标准》已更新，请各部门学习',
      keys: ['能力评估标准'],
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      title: '下周一开始进行年度健康体检，请做好准备工作',
      keys: ['健康体检', '下周一'],
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    }
  ]
  notice = Object.assign(notice, data)
}

// 获取今日提醒
let reminders = reactive<any[]>([])
const getReminders = async () => {
  const data = [
    {
      title: '张爷爷（301房）需要按时服用降压药',
      priority: '重要',
      tagType: 'danger',
      time: new Date(new Date().setHours(8, 30)),
      location: '3楼护理站',
      keys: ['张爷爷', '降压药']
    },
    {
      title: '李奶奶（205房）今日需进行血糖监测',
      priority: '普通',
      tagType: 'warning',
      time: new Date(new Date().setHours(10, 0)),
      location: '2楼医务室',
      keys: ['李奶奶', '血糖监测']
    },
    {
      title: '下午3点老年人健康讲座',
      priority: '活动',
      tagType: 'success',
      time: new Date(new Date().setHours(15, 0)),
      location: '一楼多功能厅',
      keys: ['健康讲座']
    },
    {
      title: '王爷爷（108房）家属探访',
      priority: '提醒',
      tagType: 'info',
      time: new Date(new Date().setHours(14, 30)),
      location: '接待室',
      keys: ['王爷爷', '家属探访']
    }
  ]
  reminders = Object.assign(reminders, data)
}

// 获取快捷入口
let shortcut = reactive<Shortcut[]>([])

const getShortcut = async () => {
  const data = [
    {
      name: '首页',
      icon: 'ion:home-outline',
      url: '/',
      color: '#1fdaca'
    },
    {
      name: '老人档案',
      icon: 'mdi:folder-account-outline',
      url: 'http://localhost/elders/archivesInfo',
      color: '#409EFF'
    },
    {
      name: '评估系统',
      icon: 'mdi:clipboard-check-outline',
      url: 'http://localhost/evaluation/result',
      color: '#67C23A'
    },
    {
      name: '服务计划',
      icon: 'mdi:calendar-clock',
      url: 'http://localhost/servicePlan/service-plan',
      color: '#E6A23C'
    },
    {
      name: '护理记录',
      icon: 'mdi:clipboard-text-outline',
      url: '/service/record',
      color: '#F56C6C'
    },
    {
      name: 'AI助手',
      icon: 'tabler:ai',
      url: '/ai/chat',
      color: '#7c3aed'
    }
  ]
  shortcut = Object.assign(shortcut, data)
}

// 老人健康状况分布
const getUserAccessSource = async () => {
  const data = [
    { value: 45, name: '健康' },
    { value: 30, name: '轻度失能' },
    { value: 15, name: '中度失能' },
    { value: 8, name: '重度失能' },
    { value: 2, name: '特别护理' }
  ]
  set(
    pieOptionsData,
    'legend.data',
    data.map((v) => v.name)
  )
  pieOptionsData!.series![0].data = data.map((v) => {
    return {
      name: v.name,
      value: v.value
    }
  })
}
const barOptionsData = reactive<EChartsOption>(barOptions) as EChartsOption

// 近7天护理服务统计
const getWeeklyUserActivity = async () => {
  const days: string[] = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    days.push(formatTime(date, 'MM-dd'));
  }

  const data = [
    { value: 42, name: days[0] },
    { value: 38, name: days[1] },
    { value: 45, name: days[2] },
    { value: 40, name: days[3] },
    { value: 43, name: days[4] },
    { value: 32, name: days[5] },
    { value: 36, name: days[6] }
  ]

  set(barOptionsData, 'xAxis.data', days)
  set(barOptionsData, 'series', [
    {
      name: '护理服务次数',
      data: data.map((v) => v.value),
      type: 'bar'
    }
  ])
}

const getAllApi = async () => {
  await Promise.all([
    getCount(),
    getServiceModules(),
    getNotice(),
    getReminders(),
    getShortcut(),
    getUserAccessSource(),
    getWeeklyUserActivity()
  ])
  loading.value = false
}

const handleModuleClick = (url: string) => {
  if (url === '/') {
    // 原地刷新首页
    window.location.reload()
  } else if (url.startsWith('http')) {
    // 外部链接，当前页面跳转
    window.location.href = url
  } else {
    // 内部路由
    router.push(url)
  }
}

const handleShortcutClick = (url: string) => {
  if (url === '/') {
    // 原地刷新首页
    window.location.reload()
  } else if (url.startsWith('http')) {
    // 外部链接，当前页面跳转
    window.location.href = url
  } else {
    // 内部路由
    router.push(url)
  }
}

getAllApi()
</script>
