<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" />
      </el-form-item>
      
      <!-- 新增时可以选择父级 -->
      <el-form-item label="父级分类" prop="parentId">
        <el-tree-select
          v-if="formType === 'create'"
          v-model="formData.parentId"
          :data="categoryTree"
          :props="defaultProps"
          placeholder="请选择父级分类"
          @update:modelValue="handleParentChange"
          clearable
          check-strictly
        />
        <el-input
          v-else
          v-model="formData.parentId"
          placeholder="父级分类ID"
          disabled
        />
      </el-form-item>

      <el-form-item label="层级" prop="level">
        <el-input v-model="formData.level" placeholder="层级" disabled />
      </el-form-item>

      <el-form-item label="祖级列表" prop="ancestors">
        <el-input v-model="formData.ancestors" placeholder="祖级列表" disabled />
      </el-form-item>

      <!-- 只有没有子级的分类才显示 difyKey 输入框 -->
      <el-form-item 
        v-if="!hasChildren" 
        label="Dify平台密钥" 
        prop="difyKey"
      >
        <el-input 
          v-model="formData.difyKey" 
          placeholder="请输入Dify平台密钥"
          :disabled="formLoading"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { CategoryApi, CategoryVO } from '@/api/servicePlan/serviceplancategory'

/** 服务计划分类 表单 */
defineOptions({ name: 'CategoryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const hasChildren = ref(false)
const categoryTree = ref<CategoryVO[]>([])

// 树形配置
const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true, // 允许选择任意层级
}

const formData = ref({
  id: undefined,
  name: undefined,
  parentId: undefined,
  ancestors: undefined,
  level: undefined,
  difyKey: undefined,
})

const formRules = reactive({
  name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

// 处理父级变化
const handleParentChange = async (parentId: number | null) => {
  if (!parentId) {
    // 如果清除了父级，设置为一级分类
    formData.value.level = 1
    formData.value.ancestors = ''
    return
  }

  // 查找选中的父级节点
  const findParentNode = (nodes: CategoryVO[], targetId: number): CategoryVO | null => {
    for (const node of nodes) {
      if (node.id === targetId) return node
      if (node.children) {
        const found = findParentNode(node.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  const parentNode = findParentNode(categoryTree.value, parentId)
  if (parentNode) {
    formData.value.level = (parentNode.level || 0) + 1
    formData.value.ancestors = parentNode.ancestors 
      ? `${parentNode.ancestors},${parentNode.id}` 
      : parentNode.id?.toString()
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number, parentData?: Partial<CategoryVO>) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 获取分类树数据（用于选择父级）
  if (type === 'create') {
    const treeData = await CategoryApi.generateCategoryTree()
    categoryTree.value = treeData || []
  }
  
  // 如果是新增子分类，设置父级数据
  if (type === 'create' && parentData) {
    formData.value = {
      ...formData.value,
      ...parentData,
    }
  }
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await CategoryApi.getCategory(id)
      formData.value = data
      // 检查是否有子级
      const treeData = await CategoryApi.generateCategoryTree()
      const findNode = (nodes: any[], targetId: number): boolean => {
        return nodes.some(node => {
          if (node.id === targetId) {
            return node.children && node.children.length > 0
          }
          if (node.children) {
            return findNode(node.children, targetId)
          }
          return false
        })
      }
      hasChildren.value = findNode(treeData, id)
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时默认没有子级
    hasChildren.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CategoryVO
    if (formType.value === 'create') {
      await CategoryApi.createCategory(data)
      message.success(t('common.createSuccess'))
    } else {
      await CategoryApi.updateCategory(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    parentId: undefined,
    ancestors: undefined,
    level: undefined,
    difyKey: undefined,
  }
  hasChildren.value = false
  formRef.value?.resetFields()
}
</script>