<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <!-- <el-form-item label="评估任务清单ID" prop="listId">
        <el-input v-model="formData.listId" placeholder="请输入评估任务清单ID" />
      </el-form-item>
      <el-form-item label="评估任务清单名称" prop="listName">
        <el-input v-model="formData.listName" placeholder="请输入评估任务清单名称" />
      </el-form-item>
      <el-form-item label="老人ID" prop="elderId">
        <el-input v-model="formData.elderId" placeholder="请输入老人ID" />
      </el-form-item>
      <el-form-item label="老人姓名" prop="elderName">
        <el-input v-model="formData.elderName" placeholder="请输入老人姓名" />
      </el-form-item>
      <el-form-item label="评估师ID" prop="evaluatorId">
        <el-input v-model="formData.evaluatorId" placeholder="请输入评估师ID" />
      </el-form-item>
      <el-form-item label="评估师姓名" prop="evaluatorName">
        <el-input v-model="formData.evaluatorName" placeholder="请输入评估师姓名" />
      </el-form-item>
      <el-form-item label="评估原因" prop="evaluationReason">
        <el-input v-model="formData.evaluationReason" placeholder="请输入评估原因" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_LIST_EXECUTION_STATUS)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker v-model="formData.startTime" type="date" value-format="x" placeholder="选择开始时间" />
      </el-form-item>
      <el-form-item label="完成时间" prop="endTime">
        <el-date-picker v-model="formData.endTime" type="date" value-format="x" placeholder="选择完成时间" />
      </el-form-item>
      <el-form-item label="所需完成的评估模板ID" prop="requiredTemplateIds">
        <el-input v-model="formData.requiredTemplateIds" placeholder="请输入所需完成的评估模板ID" />
      </el-form-item>
      <el-form-item label="已完成的评估模板ID" prop="completedTemplateIds">
        <el-input v-model="formData.completedTemplateIds" placeholder="请输入已完成的评估模板ID" />
      </el-form-item>
      <el-form-item label="关联的评估结果ID" prop="resultIds">
        <el-input v-model="formData.resultIds" placeholder="请输入关联的评估结果ID" />
      </el-form-item> -->
      <el-form-item label="api密钥" prop="apiKey">
        <el-input v-model="formData.apiKey" placeholder="请输入api密钥" />
      </el-form-item>
      <!-- <el-form-item label="评估师分析" prop="evaluatorAnalysis">
        <el-input v-model="formData.evaluatorAnalysis" type="textarea" :rows="3" placeholder="请输入评估师分析" />
      </el-form-item> -->
      <!-- <el-form-item label="AI输入" prop="aiInputs">
        <el-input v-model="formData.aiInputs" placeholder="请输入AI输入" />
      </el-form-item>
      <el-form-item label="AI分析" prop="aiAnalysis">
        <el-input v-model="formData.aiAnalysis" placeholder="请输入AI分析" />
      </el-form-item> -->
      <el-form-item label="综合评估结果" prop="summaryResult">
        <el-input v-model="formData.summaryResult" type="textarea" :rows="3" placeholder="请输入综合评估结果" />
        <!-- <Editor v-model="formData.summaryResult" height="300px" :defaultConfig="{
          placeholder: '请输入综合评估结果...',
          autoFocus: false
        }" />  -->
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'

defineOptions({ name: 'ListExecutionForm' })

// 国际化
const { t } = useI18n()
// 消息弹窗
const message = useMessage()
// 弹窗的是否展示
const dialogVisible = ref(false)
// 弹窗的标题
const dialogTitle = ref('')
// 表单的加载
const formLoading = ref(false)
// 表单的类型
const formType = ref('')
// 表单的数据
const formData = ref({
  id: undefined,
  listId: undefined,
  listName: undefined,
  elderId: undefined,
  elderName: undefined,
  evaluatorId: undefined,
  evaluatorName: undefined,
  evaluationReason: undefined,
  status: undefined,
  startTime: undefined,
  endTime: undefined,
  requiredTemplateIds: undefined,
  completedTemplateIds: undefined,
  resultIds: undefined,
  apiKey: undefined,
  evaluatorAnalysis: undefined,
  aiInputs: undefined,
  aiAnalysis: undefined,
  summaryResult: '',
})
// 表单的规则
const formRules = reactive({
  listId: [{ required: true, message: '评估任务清单ID不能为空', trigger: 'blur' }],
  listName: [{ required: true, message: '评估任务清单名称不能为空', trigger: 'blur' }],
  elderId: [{ required: true, message: '老人ID不能为空', trigger: 'blur' }],
  elderName: [{ required: true, message: '老人姓名不能为空', trigger: 'blur' }],
  evaluatorId: [{ required: true, message: '评估师ID不能为空', trigger: 'blur' }],
  evaluatorName: [{ required: true, message: '评估师姓名不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  startTime: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
})
// 表单的 Ref
const formRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ListExecutionApi.getListExecution(id)
      // 确保 summaryResult 不为 null
      if (formData.value.summaryResult === null || formData.value.summaryResult === undefined) {
        formData.value.summaryResult = ''
      }
    } finally {
      formLoading.value = false
    }
  }
}
// 暴露 open 方法
defineExpose({ open })

// 定义 success 事件
const emit = defineEmits(['success'])
// 提交表单
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ListExecutionVO
    if (formType.value === 'create') {
      await ListExecutionApi.createListExecution(data)
      message.success(t('common.createSuccess'))
    } else {
      await ListExecutionApi.updateListExecution(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    listId: undefined,
    listName: undefined,
    elderId: undefined,
    elderName: undefined,
    evaluatorId: undefined,
    evaluatorName: undefined,
    evaluationReason: undefined,
    status: undefined,
    startTime: undefined,
    endTime: undefined,
    requiredTemplateIds: undefined,
    completedTemplateIds: undefined,
    resultIds: undefined,
    apiKey: undefined,
    evaluatorAnalysis: undefined,
    aiInputs: undefined,
    aiAnalysis: undefined,
    summaryResult: '',
  }
  formRef.value?.resetFields()
}
</script>