<!--
 * @Date: 2025-02-21 09:17:19
 * @Author: xkk
 * @LastEditTime: 2025-02-22 22:21:50
 * @FilePath: \huiyi_manage\src\views\evaluation\elderEval\evalDetail.vue
-->
<script setup lang="ts">
import { TemplateApi } from '@/api/evaluation/template'
import { ResultApi } from '@/api/evaluation/result'
import formCreate from '@form-create/element-ui'

defineOptions({
  name: 'EvalDetail'
})

const route = useRoute()
const message = useMessage()

// 表单相关
const previewForm = ref({})
const previewApi = ref(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: { show: false }, // 不显示提交按钮
  resetBtn: { show: false }, // 不显示重置按钮
  disabled: true // 禁用所有表单项
})

// 评估基础信息
const evalInfo = reactive({
  elderId: undefined as number | undefined,
  elderName: '',
  templateId: undefined as number | undefined,
  templateName: '',
  evaluatorId: undefined as number | undefined,
  evaluatorName: '',
  evaluationTime: '',
  result: ''
})

// 加载模板数据和评估结果
const loadData = async () => {
  try {
    // 加载评估基础信息
    const query = route.query
    evalInfo.elderId = parseInt(query.elderId as string)
    evalInfo.elderName = query.elderName as string
    evalInfo.templateId = parseInt(query.templateId as string)
    evalInfo.templateName = query.templateName as string
    evalInfo.evaluatorId = parseInt(query.evaluatorId as string)
    evalInfo.evaluatorName = query.evaluatorName as string
    evalInfo.evaluationTime = query.evaluationTime as string
    evalInfo.result = query.result as string
    // 设置表单值
    if (evalInfo.result) {
      const resultData = JSON.parse(evalInfo.result)
      templateRule.value = resultData.rules

      // 从评估结果中恢复表单值
      const formValues: any = {}
      resultData.assessmentResults.forEach((result: any) => {
        result.items.forEach((item: any) => {
          // 使用field作为key，而不是name
          const rule = item.props && item.props.field ? item.props.field : item.name
          formValues[rule] = item.value
        })
      })
      previewForm.value = formValues
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<template>
  <ContentWrap>
    <div class="evaluation-form">
      <form-create
        disabled
        v-model="previewForm"
        v-model:api="previewApi"
        :rule="templateRule"
        :option="templateOption"
      />
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.evaluation-form {
  padding: 20px;
  background: #fff;
  border-radius: 4px;

  :deep(.el-form) {
    max-width: 800px;
    margin: 0 auto;
  }
}
</style>
