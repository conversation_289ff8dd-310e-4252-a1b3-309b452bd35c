<template>
  <Dialog :title="title" v-model="dialogVisible" @close="handleClose" width="1200px">
    <div class="flex mb-4">
      <!-- 左侧表单 -->
      <TaskForm
        ref="taskFormRef"
        :form="form"
        @update:form="form = $event"
        :rules="rules"
        :loading="loading"
        :categoryTree="categoryTree"
        :categoryProps="categoryProps"
        :userList="userList"
        :selectedExecutors="selectedExecutors"
        @handleExecutorsChange="handleExecutorsChange"
        @handleRecurringChange="handleRecurringChange"
        @openRepeatRuleDialog="openRepeatRuleDialog"
        @handleCategoryChange="handleCategoryChange"
        @updateCategoryTree="updateCategoryTree"
      />

      <!-- 垂直分割线 -->
      <div class="vertical-divider"></div>

      <!-- 右侧内容：日期预览和冲突检测 -->
      <div class="right-column">
        <!-- 日期预览 -->
        <TaskDatesPreview 
          :generatedDates="generatedDates"
          @update:generatedDates="generatedDates = $event"
        />

        <!-- 冲突检测部分 -->
        <ConflictCheck
          :generatedDates="generatedDates"
          :form="form"
          :selectedExecutors="selectedExecutors"
          :userList="userList"
          :planInfo="props.planInfo"
        />
      </div>
    </div>
    
    <!-- 下半部分：执行细则 -->
    <div class="execution-details">
      <el-form-item label="执行细则" prop="description">
        <MarkdownEditor
          v-model="form.description"
          :ai-button-disabled="isAiButtonDisabled"
          :ai-reasoning="aiReasoning"
          :is-thinking="isThinking"
          @generate="generateDescription"
        />
      </el-form-item>
    </div>

    <template #footer>
      <el-button @click="handleClose">取 消</el-button>
      <el-button 
        type="warning" 
        @click="handleSubmit(false)" 
        :loading="loading"
      >
        暂存
      </el-button>
      <el-button 
        v-if="isTaskInProgress" 
        type="danger" 
        @click="clearTaskExecutors"
        :loading="clearLoading"
      >
        清空执行人分配
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit(true)" 
        :loading="loading"
        :disabled="isTaskInProgress || !canAssignExecutors"
      >
        保存并分配执行人
      </el-button>
    </template>

    <!-- 重复规则弹窗 -->
    <RepeatRuleDialog
      v-model:visible="repeatRuleVisible"
      :selected-plan="planInfo"
      @confirm="handleRepeatRuleConfirm"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { PlanExecutorApi } from '@/api/servicePlan/planexecutor'
import RepeatRuleDialog from './RepeatRuleDialog.vue'
import MarkdownEditor from '@/components/MarkdownEditor/index.vue'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'
import TaskForm from './TaskForm.vue'
import TaskDatesPreview from './TaskDatesPreview.vue'
import ConflictCheck from './ConflictCheck.vue'

// 扩展 dayjs
dayjs.extend(isSameOrBefore)

// 定义用户列表类型
const userList = ref<UserVO[]>([])
const elderList = ref<UserVO[]>([])

// 定义 AI 响应数据类型
interface AIChoice {
  delta: {
    reasoning_content?: string;
    content?: string;
  };
  finish_reason?: string;
}

interface AIResponse {
  choices?: AIChoice[];
}

interface TaskFormData {
  planId?: number 
  elderId?: number
  name?: string
  taskType?: string
  status?: number
  isRecurring?: number
  repeatPattern?: string
  executeDate?: string
  startTime?: string
  endTime?: string
  recommendedExecutors?: string
  priority?: number
  description?: string
  categoryId?: number
}

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  selectedDate: {
    type: Date,
    default: null
  },
  taskId: {
    type: Number,
    default: undefined
  },
  planInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 表单数据
const taskFormRef = ref()
const formRef = computed(() => taskFormRef.value?.$refs?.formRef)
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
const loading = ref(false)
const title = computed(() => props.taskId ? '编辑任务' : '新建任务')

const form = ref<TaskFormData>({
  name: undefined,
  taskType: undefined,
  status: 1,
  isRecurring: 0,
  repeatPattern: undefined,
  executeDate: props.selectedDate ? dayjs(props.selectedDate).format('YYYY-MM-DD') : undefined,
  startTime: undefined,
  endTime: undefined,
  recommendedExecutors: undefined,
  priority: 4,
  description: undefined,
  categoryId: undefined,
})

// 选中的执行人
const selectedExecutors = ref<number[]>([])
// 重复规则弹窗控制
const repeatRuleVisible = ref(false)
// 生成的任务日期列表
const generatedDates = ref<string[]>([])
// 冲突状态
const elderConflicts = ref<any[]>([])
const executorConflicts = ref<any[]>([])

// 添加清空加载状态
const clearLoading = ref(false)

// 分类树数据
const categoryTree = ref<any[]>([])

// 分类树配置
const categoryProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true,
}

// 获取用户列表
const getUserList = async () => {
  try {
    const data = await getSimpleUserList()
    userList.value = data
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 获取老人列表
const getElderList = async () => {
  try {
    const data = await getSimpleUserList()
    elderList.value = data
  } catch (error) {
    console.error('获取老人列表失败:', error)
  }
}

// 处理执行人变化
const handleExecutorsChange = (value: number[]) => {
  // 更新选中的执行人数组
  selectedExecutors.value = value
  
  // 表单中的 recommendedExecutors 字段会通过 TaskForm 组件的 updateFormField 方法更新
}

// 处理周期任务选择变化
const handleRecurringChange = (value: number) => {
  if (value === 0) {
    form.value.repeatPattern = undefined
    form.value.executeDate = undefined
  }
}

// 打开重复规则弹窗
const openRepeatRuleDialog = () => {
  repeatRuleVisible.value = true
}

// 处理重复规则确认
const handleRepeatRuleConfirm = (pattern: string) => {
  form.value.repeatPattern = pattern
}

// 格式化冲突文本
const formatConflictText = (conflict: any): string => {
  const startTime = dayjs(conflict.startTime).format('YYYY-MM-DD HH:mm')
  const endTime = dayjs(conflict.endTime).format('YYYY-MM-DD HH:mm')
  const taskInfo = `${conflict.planName || '未知计划'}`
  const personInfo = conflict.executorName 
    ? `执行人: ${conflict.executorName}` 
    : `老人: ${conflict.elderName}`
  
  return `${startTime} 至 ${endTime}\n${taskInfo} (${personInfo})`
}

// 检测老人时间冲突
const checkElderConflict = async () => {
  try {
    const response = await PlanExecutorApi.checkElderConflict({
      elderId: props.planInfo.elderId,
      dates: generatedDates.value,
      startTime: form.value.startTime!,
      endTime: form.value.endTime!
    })

    elderConflicts.value = response
    if (response.length > 0) {
      ElMessage.warning(`发现 ${response.length} 个老人时间冲突`)
    } else {
      ElMessage.success('未发现老人时间冲突')
    }
  } catch (error) {
    console.error('检测老人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

// 检测执行人时间冲突
const checkExecutorConflict = async () => {
  try {
    executorConflicts.value = []
    for (const executorId of selectedExecutors.value) {
      const response = await PlanExecutorApi.checkExecutorConflict({
        executorId,
        dates: generatedDates.value,
        startTime: form.value.startTime!,
        endTime: form.value.endTime!
      })

      if (response.length > 0) {
        const executor = userList.value.find(user => user.id === executorId)
        executorConflicts.value.push(...response.map(conflict => ({
          ...conflict,
          executorName: executor?.nickname || executorId
        })))
        ElMessage.warning(`执行人 ${executor?.nickname || executorId} 有 ${response.length} 个时间冲突`)
      }
    }
    
    if (executorConflicts.value.length === 0) {
      ElMessage.success('未发现执行人时间冲突')
    }
  } catch (error) {
    console.error('检测执行人冲突失败:', error)
    ElMessage.error('检测失败')
  }
}

// 判断是否可以检测冲突
const canCheckElderConflict = computed(() => {
  return (
    generatedDates.value.length > 0 &&
    form.value.startTime &&
    form.value.endTime
  )
})

const canCheckExecutorConflict = computed(() => {
  return (
    selectedExecutors.value.length > 0 && 
    generatedDates.value.length > 0 &&
    form.value.startTime &&
    form.value.endTime
  )
})

// 判断任务是否处于进行中状态
const isTaskInProgress = computed(() => {
  return form.value.status === 2 // 2-进行中
})

// 添加是否可以分配执行人的计算属性
const canAssignExecutors = computed(() => {
  return (
    selectedExecutors.value.length > 0 && 
    generatedDates.value.length > 0 &&
    form.value.startTime &&
    form.value.endTime
  )
})

// 清空执行人分配
const clearTaskExecutors = async () => {
  if (!props.taskId) return
  
  try {
    await ElMessageBox.confirm('确认清空该任务的执行人分配信息？', '警告', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    clearLoading.value = true
    
    try {
      // 1. 先删除执行人分配
      await PlanExecutorApi.deleteByTaskId(props.taskId)
      
      // 2. 删除成功后，再更新任务状态为待分配
      await TaskApi.updateTaskStatus(props.taskId, 1) // 1-待分配
      
      ElMessage.success('清空执行人分配成功')
      emit('success') // 通知父组件刷新
      dialogVisible.value = false
    } catch (error) {
      // 如果删除执行人分配失败，不执行状态更新
      console.error('清空执行人分配失败:', error)
      throw error // 向上抛出错误，触发外层的错误处理
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空执行人分配失败')
    }
  } finally {
    clearLoading.value = false
  }
}

// 提交表单
const handleSubmit = async (assignExecutors: boolean) => {
  if (!formRef.value) return
  //检查执行细则
  if (!form.value.description) {
    ElMessage.error('请输入执行细则')
    return
  }
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      
      // 只在保存并分配时才检查冲突
      if (assignExecutors) {
        await checkElderConflict()
        await checkExecutorConflict()
        
        if (elderConflicts.value.length > 0 || executorConflicts.value.length > 0) {
          const confirmResult = await ElMessageBox.confirm(
            `检测到以下冲突：
            ${elderConflicts.value.length > 0 ? `\n- ${elderConflicts.value.length}个老人时间冲突` : ''}
            ${executorConflicts.value.length > 0 ? `\n- ${executorConflicts.value.length}个执行人时间冲突` : ''}
            \n是否仍要继续保存？`,
            '时间冲突提醒',
            {
              confirmButtonText: '继续保存',
              cancelButtonText: '取消',
              type: 'warning',
            }
          ).catch(() => false)

          if (!confirmResult) {
            return
          }
        }
      }

      loading.value = true
      try {
        const submitData: Partial<TaskVO> = {
          planId: props.planInfo.id,
          elderId: props.planInfo.elderId,
          recommendedExecutors: form.value.recommendedExecutors,
          executeDates: generatedDates.value,
          status: assignExecutors ? 1 : 0,
          ...form.value
        }

        let taskId: number
        
        // 区分新建和编辑场景
        if (props.taskId) {
          // 编辑任务
          submitData.id = props.taskId
          await TaskApi.updateTask(submitData as TaskVO)
          taskId = props.taskId
        } else {
          // 新建任务
          taskId = await TaskApi.createTask(submitData as TaskVO)
        }

        // 如果是保存并分配，则调用批量保存执行人接口
        if (assignExecutors && taskId) {
          // 构建批量创建执行人的请求数据
          const batchCreateReqVO = {
            taskId: taskId,
            planId: props.planInfo.id,
            elderId: props.planInfo.elderId,
            executorIds: selectedExecutors.value,
            dates: generatedDates.value,
            startTime: form.value.startTime,
            endTime: form.value.endTime,
            status: 1 // 执行状态，1-待执行
          }
          
          try {
            await PlanExecutorApi.batchCreatePlanExecutor(batchCreateReqVO)
          } catch (error) {
            console.error('批量创建执行人失败:', error)
            throw error
          }
        }

        ElMessage.success(props.taskId ? '修改成功' : '创建成功')
        emit('success')
        dialogVisible.value = false
      } catch (error) {
        console.error('保存任务失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.value = {
    name: undefined,
    status: 1,
    isRecurring: 0,
    repeatPattern: undefined,
    executeDate: undefined,
    startTime: undefined,
    endTime: undefined,
    recommendedExecutors: undefined,
    priority: 4,
    description: undefined,
    categoryId: undefined,
    taskType: undefined,
  }

  // 重置其他相关状态
  selectedExecutors.value = []
  generatedDates.value = []
  elderConflicts.value = []
  executorConflicts.value = []
  // 清空 AI 思考过程内容
  aiReasoning.value = ''
}

// 监听日期变化
watch(
  [
    () => form.value.executeDate,
    () => form.value.isRecurring,
    () => form.value.repeatPattern
  ],
  async () => {
    if (form.value.isRecurring === 0) {
      if (form.value.executeDate) {
        generatedDates.value = [form.value.executeDate]
        // 为非周期任务生成单日的周期规则
        form.value.repeatPattern = JSON.stringify({
          type: 'daily',
          startDate: form.value.executeDate,
          endDate: form.value.executeDate,
          interval: 1
        })
      } else {
        generatedDates.value = []
        form.value.repeatPattern = undefined
      }
      return
    }

    if (form.value.isRecurring === 1 && form.value.repeatPattern) {
      try {
        const pattern = JSON.parse(form.value.repeatPattern)
        const dates = generateDatesFromPattern(pattern)
        if (dates.length > 0) {
          generatedDates.value = dates
        }
      } catch (error) {
        console.error('解析重复规则失败:', error)
      }
    }
  },
  { immediate: true, deep: true }
)

// 添加日期生成函数
const generateDatesFromPattern = (pattern: any): string[] => {
  const dates: string[] = []
  const startDate = dayjs(pattern.startDate)
  const endDate = dayjs(pattern.endDate)
  let currentDate = startDate
  switch (pattern.type) {
    case 'daily':
      currentDate = startDate
      while (currentDate.isSameOrBefore(endDate)) {
        dates.push(currentDate.format('YYYY-MM-DD'))
        currentDate = currentDate.add(pattern.interval || 1, 'day')
      }
      break
      
    case 'weekly':
      if (pattern.days?.length) {
        currentDate = startDate
        while (currentDate.isSameOrBefore(endDate)) {
          const weekday = currentDate.day() || 7 // 转换周日的0为7
          if (pattern.days.includes(weekday)) {
            dates.push(currentDate.format('YYYY-MM-DD'))
          }
          currentDate = currentDate.add(1, 'day')
        }
      }
      break
      
    case 'monthly':
      if (pattern.days?.length) {
        currentDate = startDate
        while (currentDate.isSameOrBefore(endDate)) {
          const dayOfMonth = currentDate.date()
          if (pattern.days.includes(dayOfMonth)) {
            dates.push(currentDate.format('YYYY-MM-DD'))
          }
          currentDate = currentDate.add(1, 'day')
        }
      }
      break
  }
  
  return dates.sort()
}

// 添加表单校验规则
const rules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  description: [{ required: true, message: '请输入执行细则', trigger: 'blur' }],
  categoryId: [
    { required: true, message: '请选择任务分类', trigger: 'change' }
  ],
}

// 初始化数据
onMounted(() => {
  getElderList()
  getUserList()
  getCategoryTree()
})

/** 打开弹窗 */
const open = async () => {
  if (props.taskId) {
    loading.value = true
    try {
      // 获取分类树数据
      await getCategoryTree()
      
      const data = await TaskApi.getTask(props.taskId)
      // 处理数据转换
      form.value = {
        planId: data.planId,
        elderId: data.elderId,
        name: data.name, // 确保设置名称
        categoryId: data.categoryId,
        taskType: data.taskType,
        status: data.status,
        isRecurring: Number(data.isRecurring),
        repeatPattern: data.repeatPattern,
        startTime: data.startTime,
        endTime: data.endTime,
        priority: data.priority,
        description: data.description,
      }
      
      // 如果是非周期任务，从重复规则中提取执行日期
      if (form.value.isRecurring === 0 && form.value.repeatPattern) {
        try {
          const pattern = JSON.parse(form.value.repeatPattern)
          form.value.executeDate = pattern.startDate
        } catch (error) {
          console.error('解析重复规则失败:', error)
        }
      }
      
      // 处理推荐执行人
      if (data.recommendedExecutors) {
        try {
          const executors = JSON.parse(data.recommendedExecutors)
          selectedExecutors.value = Array.isArray(executors) ? executors : []
          form.value.recommendedExecutors = data.recommendedExecutors
        } catch (error) {
          console.error('解析执行人失败:', error)
        }
      }
      
      // 更新生成的日期
      if (form.value.isRecurring === 1 && form.value.repeatPattern) {
        try {
          const pattern = JSON.parse(form.value.repeatPattern)
          generatedDates.value = generateDatesFromPattern(pattern)
        } catch (error) {
          console.error('生成日期失败:', error)
        }
      } else if (form.value.executeDate) {
        generatedDates.value = [form.value.executeDate]
      }
      
    } catch (error) {
      console.error('获取任务数据失败:', error)
      ElMessage.error('获取任务数据失败')
    } finally {
      loading.value = false
    }
  } else {
    // 新建任务时的处理
    form.value = {
      planId: props.planInfo.id,
      elderId: props.planInfo.elderId,
      name: '',
      categoryId: undefined,
      taskType: undefined,
      status: 1,
      isRecurring: 0,
      repeatPattern: undefined,
      executeDate: props.selectedDate ? dayjs(props.selectedDate).format('YYYY-MM-DD') : undefined,
      startTime: undefined,
      endTime: undefined,
      recommendedExecutors: undefined,
      priority: 4,
      description: '',
    }
    generatedDates.value = props.selectedDate ? [dayjs(props.selectedDate).format('YYYY-MM-DD')] : []
    selectedExecutors.value = []
    
    // 获取分类树数据
    await getCategoryTree()
  }
  
  dialogVisible.value = true
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    open()
  }
})

// 生成 AI 描述
const aiReasoning = ref<string>('')
const isThinking = ref(false)
const abortController = ref<AbortController | null>(null)
// 修改 AI 按钮禁用条件
const isAiButtonDisabled = computed(() => {
  return !form.value.categoryId // 只要选择了分类就可以使用 AI
})

// 修改生成 AI 描述函数
const generateDescription = async () => {
  // 获取分类详情以获取 dify_key
  let difyKey = ''
  if (form.value.categoryId) {
    try {
      const categoryDetail = await TaskCategoryApi.getTaskCategory(form.value.categoryId)
      difyKey = categoryDetail.difyKey
      if (!difyKey) {
        ElMessage.error('该分类未配置 AI 接口密钥')
        return
      }
    } catch (error) {
      console.error('获取分类详情失败:', error)
      ElMessage.error('获取分类详情失败')
      return
    }
  } else {
    ElMessage.error('请先选择任务分类')
    return
  }

  // 创建新的 AbortController
  abortController.value = new AbortController()

  try {
    isThinking.value = true
    form.value.description = ''
    aiReasoning.value = ''

    // 根据老人id获取老人信息
    const elderInfo = await ArchivesProfileApi.getArchivesProfile(props.planInfo.elderId)
    const elderName = elderInfo.name
    const elderAge = new Date().getFullYear() - new Date(elderInfo.birthDate).getFullYear()

    // 准备执行日期信息
    let executeDateInfo = ''
    if (form.value.isRecurring === 1) {
      // 周期性任务，使用生成的所有日期
      if (generatedDates.value.length > 0) {
        executeDateInfo = `执行日期：周期性任务，共${generatedDates.value.length}个执行日期\n具体日期：${generatedDates.value.join('、')}`
      } else {
        executeDateInfo = '执行日期：周期性任务（尚未生成具体日期）'
      }
    } else {
      // 非周期性任务，使用单个日期
      executeDateInfo = `执行日期：${form.value.executeDate || '未设置'}`
    }

    const aiInputs = "请根据以下信息生成任务执行细则：\n" +
      `任务类型：${form.value.taskType}\n` +
      `${executeDateInfo}\n` +
      `执行时间：${form.value.startTime || '未设置'} - ${form.value.endTime || '未设置'}\n` +
      `老人姓名：${elderName}\n` +
      `老人年龄：${elderAge}\n`

    const options_ai = {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${difyKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        inputs: {},
        query: aiInputs,
        response_mode: 'streaming',
        conversation_id: '',
        user: 'user123'
      }),
      signal: abortController.value.signal // 添加 signal
    }

    const response = await fetch('http://172.30.22.125/v1/chat-messages', options_ai);
    if (!response.body) throw new Error('Response body is null');

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let isDone = false;
    let buffer = '';
    
    // 完整响应内容
    let currentContent = '';
    
    // 处理SSE格式的流数据
    function parseSSE(data: string) {
      const lines = data.split('\n').filter((line) => line.trim() !== '')
      return lines
        .map((line) => {
          if (line.startsWith('data: ')) {
            try {
              return JSON.parse(line.slice(6))
            } catch (error) {
              console.error('解析 SSE 数据失败:', line, error)
              return null
            }
          }
          return null
        })
        .filter(Boolean)
    }
    
    while (!isDone) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      
      buffer += chunk;
      const chunks = buffer.split('\n\n');
      
      chunks.slice(0, -1).forEach((chunk) => {
        const events = parseSSE(chunk);
        
        events.forEach((data: any) => {
          if (data.event === 'message' && data.answer) {
            // 累积完整内容
            currentContent += data.answer;
            
            // 检查是否包含完整的思考标签对
            if (currentContent.includes('</think>')) {
              const parts = currentContent.split('</think>');
              if (parts.length >= 2) {
                // 提取思考内容
                aiReasoning.value = parts[0].replace(/<think>/g, '').trim();
                
                // 提取回答内容
                form.value.description = parts[1].trim();
              }
            } else {
              // 暂时将所有内容视为思考内容
              aiReasoning.value = currentContent.replace(/<think>/g, '').trim();
            }
          }
        });
      });
      
      buffer = chunks[chunks.length - 1];
    }
    
    // 最终处理
    if (currentContent.includes('</think>')) {
      const parts = currentContent.split('</think>');
      if (parts.length >= 2) {
        // 提取思考内容
        aiReasoning.value = parts[0].replace(/<think>/g, '').trim();
        
        // 提取回答内容
        form.value.description = parts[1].trim();
      }
    } else {
      // 如果没有结束标签，将所有内容视为思考内容
      aiReasoning.value = currentContent.replace(/<think>/g, '').trim();
      form.value.description = ''; // 清空回答内容
    }
    
  } catch (error: any) {
    // 区分是否为主动取消的错误
    if (error.name === 'AbortError') {
      console.log('AI 请求已取消')
    } else {
      console.error('获取 AI 描述失败:', error)
      ElMessage.error('获取 AI 描述失败')
    }
  } finally {
    isThinking.value = false
    abortController.value = null
  }
}

// 监听弹窗关闭
watch(() => dialogVisible.value, (visible) => {
  if (!visible && abortController.value) {
    // 终止正在进行的 AI 请求
    abortController.value.abort()
    abortController.value = null
    isThinking.value = false
  }
})

// 处理分类变化
const handleCategoryChange = (categoryId: number | null) => {
  if (!categoryId) {
    form.value.categoryId = undefined
    form.value.taskType = undefined
    return
  }
  
  // 查找分类名称
  const findCategory = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return node
      }
      if (node.children) {
        const found = findCategory(node.children)
        if (found) return found
      }
    }
    return null
  }

  const category = findCategory(categoryTree.value)
  form.value.categoryId = categoryId
  form.value.taskType = category?.name
}

// 获取分类树数据
const getCategoryTree = async () => {
  try {
    const treeData = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = treeData || []
  } catch (error) {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  }
}

// 添加更新分类树的方法
const updateCategoryTree = (treeData) => {
  categoryTree.value = treeData
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.vertical-divider {
  width: 1px;
  background-color: var(--el-border-color-lighter);
  margin: 0 -1px;
}

.right-column {
  width: 45%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.execution-details {
  border-top: 1px solid var(--el-border-color);
  padding-top: 20px;
  margin-top: 20px;
  
  :deep(.el-form-item__label) {
    float: none;
    display: block;
    text-align: left;
    margin-bottom: 8px;
  }
  
  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}
</style> 
