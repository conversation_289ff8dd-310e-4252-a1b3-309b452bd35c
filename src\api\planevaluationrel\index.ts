import request from '@/config/axios'

// 服务计划评估关联 VO
export interface PlanEvaluationRelVO {
  id: number // 自增主键
  servicePlanId: number // 关联服务计划
  evaluationResultId: number // 关联评估结果
}

// 服务计划评估关联保存 VO
export interface PlanEvaluationRelSaveReqVO {
  servicePlanId: number // 关联服务计划
  evaluationResultIds: number[] // 关联评估结果ID数组
}

// 服务计划评估关联 API
export const PlanEvaluationRelApi = {

  // 新增服务计划评估关联
  createPlanEvaluationRel: async (data: PlanEvaluationRelSaveReqVO): Promise<number[]> => {
    return await request.post({ url: `/service/plan-evaluation-rel/create`, data })
  },

  // 修改服务计划评估关联
  updatePlanEvaluationRel: async (data: PlanEvaluationRelSaveReqVO): Promise<boolean> => {
    return await request.put({ url: `/service/plan-evaluation-rel/update`, data })
  },

  // 根据服务计划ID获取评估结果ID列表
  getEvaluationResultIds: async (servicePlanId: number): Promise<number[]> => {
    return await request.get({ 
      url: `/service/plan-evaluation-rel/get`,
      params: { servicePlanId }
    })
  },
}
