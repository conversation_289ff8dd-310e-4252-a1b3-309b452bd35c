import request from '@/config/axios'

// 任务冲突日志 VO
export interface ConflictLogVO {
  id: number // 自增主键
  taskId: number // 关联冲突的任务ID
  executorId: number // 执行人ID
  conflictType: string // 冲突类型：time/executor
  conflictData: string // 冲突详情
  resolveStatus: number // 解决状态：1-待处理 2-已解决 3-已忽略
  resolveComment: string // 解决备注
}

// 任务冲突日志 API
export const ConflictLogApi = {
  // 查询任务冲突日志分页
  getConflictLogPage: async (params: any) => {
    return await request.get({ url: `/servicePlan/conflict-log/page`, params })
  },

  // 查询任务冲突日志详情
  getConflictLog: async (id: number) => {
    return await request.get({ url: `/servicePlan/conflict-log/get?id=` + id })
  },

  // 新增任务冲突日志
  createConflictLog: async (data: ConflictLogVO) => {
    return await request.post({ url: `/servicePlan/conflict-log/create`, data })
  },

  // 修改任务冲突日志
  updateConflictLog: async (data: ConflictLogVO) => {
    return await request.put({ url: `/servicePlan/conflict-log/update`, data })
  },

  // 删除任务冲突日志
  deleteConflictLog: async (id: number) => {
    return await request.delete({ url: `/servicePlan/conflict-log/delete?id=` + id })
  },

  // 导出任务冲突日志 Excel
  exportConflictLog: async (params) => {
    return await request.download({ url: `/servicePlan/conflict-log/export-excel`, params })
  },

  // 更新任务冲突日志状态
  updateConflictLogStatus: async (data: {
    taskExecutorId: number
    resolveStatus: number
    resolveComment: string
  }) => {
    return await request.put({ url: `/servicePlan/conflict-log/update-status`, data })
  }
}
